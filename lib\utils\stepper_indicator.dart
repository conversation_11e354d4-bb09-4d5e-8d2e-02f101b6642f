import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/controllers.dart';

class StepperIndicator extends StatelessWidget {
  final int currentStep;

  const StepperIndicator({super.key, required this.currentStep});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 300.w,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildStepIndicator(0, 'event'.tr),
          Expanded(
              child: Divider(
            color: currentStep >= 1 ? primaryColor : Colors.grey.shade300,
          )),
          _buildStepIndicator(1, 'time_location'.tr),
          Expanded(
              child: Divider(
            color: currentStep >= 2 ? primaryColor : Colors.grey.shade300,
          )),
          _buildStepIndicator(2, 'tickets'.tr),
          Expanded(
              child: Divider(
            color: currentStep >= 3 ? primaryColor : Colors.grey.shade300,
          )),
          _buildStepIndicator(3, 'socials'.tr),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String label) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CircleAvatar(
          radius: 10.spMin,
          backgroundColor:
              currentStep >= step ? primaryColor : Colors.grey.shade300,
          child: currentStep > step
              ? Icon(Icons.done, size: 12.spMin, color: Colors.white)
              : FittedBox(
                  child: Text(
                    "${step + 1}",
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
        ),
        SizedBox(height: 4.h),
        Text(
          label,
          maxLines: 2,
          style: TextStyle(
              fontSize: 12.sp,
              color: currentStep >= step ? primaryColor : Colors.grey
              //step + 1 == currentStep + 1 ? primaryColor : Colors.grey,
              ),
        ),
      ],
    );
  }
}
