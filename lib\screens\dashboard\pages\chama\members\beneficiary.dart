import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/configs/country_specifics.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/chamaDto.dart';
import 'package:onekitty/screens/widgets/payment_radio.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/utils_exports.dart';
import 'package:get/get.dart';

class Beneficiary extends StatefulWidget {
  const Beneficiary({super.key});

  @override
  State<Beneficiary> createState() => _BeneficiaryState();
}

class _BeneficiaryState extends State<Beneficiary>
    with TickerProviderStateMixin {
  late TabController tabController;

  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController dataController = Get.put(ChamaDataController());
  KittyController kittyController = Get.put(KittyController());

  TextEditingController benfName = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController paybillController = TextEditingController();
  TextEditingController accountController = TextEditingController();
  TextEditingController tillController = TextEditingController();

  final GlobalKey _tooltipKey = GlobalKey();
  final GlobalKey _tooltipKey1 = GlobalKey();

  String? selectedChannel = "M-Pesa";

  PhoneNumber num = CountryConfig.phoneNumber;
  String setPhone = "";
  @override
  void initState() {
    initials();
    tabController = TabController(
      length: 3,
      vsync: this,
    );
    final mode = dataController.singleBenf.value.beneficiaries;
    tabController.index = (mode!.first.transferMode == "WALLET")
        ? 0
        : (mode.first.transferMode == "PAYBILL")
            ? 1
            : 2;
    super.initState();
  }

  void initials() {
    benfName.text = dataController.singleBenf.value.member!.firstName ??
        "" '${dataController.singleBenf.value.member!.secondName ?? ""}';

    phoneController.text =
        dataController.singleBenf.value.member!.phoneNumber!.substring(3);
    setPhone = phoneController.text;
    paybillController.text =
        dataController.singleBenf.value.beneficiaries!.first.accountNumber ??
            "";
    accountController.text =
        dataController.singleBenf.value.beneficiaries!.first.accountNumberRef ??
            "";
    tillController.text =
        dataController.singleBenf.value.beneficiaries!.first.accountNumberRef ??
            "";
  }

  @override
  void dispose() {
    tabController = TabController(
      length: 3,
      vsync: this,
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar: buildAppBar(context),
        body: Padding(
          padding: const EdgeInsets.all(24.0),
          child: SingleChildScrollView(
            child: Column(
              children: [
                const RowAppBar(),
                Text(
                  'edit_beneficiary_payment_details'.tr,
                  style: const  TextStyle(
                    fontSize: 25,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: 20.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      dataController.singleChamaDts.value.title ?? "",
                      style: CustomTextStyles.labelMediumff545963,
                      overflow: TextOverflow.ellipsis,
                    ),
                    // SizedBox(
                    //   width: 15.w,
                    // ),
                    // _buildActive(context)
                  ],
                ),
                SizedBox(height: 8.h),
                Text('amount_to_receive'.tr),

                Text(
                    FormattedCurrency.getFormattedCurrency(
                        dataController.singleBenf.value.amountToReceive),
                    style: CustomTextStyles.titleMediumSemiBold),
                SizedBox(height: 20.h),
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    'beneficiary_member_details'.tr,
                    style: CustomTextStyles.titleSmallGray900,
                  ),
                ),
                SizedBox(height: 2.h),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Card(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                dataController
                                        .singleBenf.value.member!.firstName ??
                                    "" '${dataController.singleBenf.value.member!.secondName ?? ""}',
                                style: const TextStyle(
                                    fontSize: 16.0, color: Colors.grey),
                              ),
                              const Spacer(),
                              Text(
                                dataController
                                        .singleBenf.value.member!.status ??
                                    "",
                                style: const TextStyle(
                                    fontSize: 16.0, color: Colors.green),
                              ),
                            ],
                          ),
                          Text(
                            dataController
                                    .singleBenf.value.member!.phoneNumber ??
                                "",
                            style: const TextStyle(
                                fontSize: 16.0, color: Colors.grey),
                          ),
                          Text(
                            dataController.singleBenf.value.member!.role ?? "",
                            style: const TextStyle(
                                fontSize: 16.0, color: Colors.grey),
                          ),
                          Text(
                            '${'at_receiving_order'.tr}${dataController.singleBenf.value.member!.receivingOrder ?? ""}',
                            style: const TextStyle(
                                fontSize: 16.0, color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                SizedBox(
                  height: 20.h,
                ),
                Row(
                  children: [
                    Text(
                      "Beneficiary Payment Channel",
                      style: CustomTextStyles.titleSmallGray900,
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        final dynamic tooltip = _tooltipKey.currentState;
                        tooltip.ensureTooltipVisible();
                      },
                      child: Tooltip(
                        key: _tooltipKey,
                        message: KtStrings.benfChannel,
                        child: CustomImageView(
                          imagePath: AssetUrl.imgInbox,
                          height: 15.h,
                          width: 15.w,
                          margin:
                              EdgeInsets.only(left: 3.w, top: 2.h, bottom: 3.h),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 5.h,
                ),

                /////
                ///
                ///
                ///
                //
                _buildTabs(BuildContext, context),
                SizedBox(
                  height: 20.h,
                ),
                Obx(() => CustomKtButton(
                    isLoading: chamaController.isUpdatingM.isTrue,
                    onPress: () async {
                      String acc = "";
                      String acc_ref = "";
                      String trns_mode = "";

                      if (tabController.index == 0) {
                        acc = setPhone.trim();
                        acc_ref = setPhone.trim();
                        trns_mode = "WALLET";
                      } else if (tabController.index == 1) {
                        acc = paybillController.text.trim();
                        acc_ref = accountController.text.trim();
                        trns_mode = "PAYBILL";
                      } else {
                        acc_ref = tillController.text.trim();
                        trns_mode = "TILL";
                        acc = tillController.text.trim();
                      }

                      final channel = kittyController
                          .getNetworkCode(networkTitle: selectedChannel ?? "")
                          .toString();

                      final updtDto = UpdateBenf(
                        id: dataController
                                .singleBenf.value.beneficiaries!.single.id ??
                            0,
                        memberId:
                            dataController.singleBenf.value.member!.id ?? 0,
                        chamaId: dataController.singleBenf.value.beneficiaries!
                                .single.chamaId ??
                            0,
                        transferMode: trns_mode,
                        paymentChannel: int.parse(channel),
                        Channel: int.parse(channel),
                        account: acc,
                        accountRef: acc_ref,
                        ratio: dataController
                                .singleBenf.value.beneficiaryPercentage ??
                            0.0,
                      );

                      final res =
                          await chamaController.updateBenfAcc(updtDto: updtDto);
                      if (res) {
                        ToastUtils.showSuccessToast(context,
                            chamaController.apiMessage.toString(), "Success");
                        Get.back();
                      } else {
                        ToastUtils.showErrorToast(context,
                            chamaController.apiMessage.toString(), "Error");
                        Get.back();
                      }
                    },
                    btnText: "Save")),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabs(BuildContext, context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 0),
      child: DefaultTabController(
        length: 3,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(5.h),
              decoration: AppDecoration.fillSlate.copyWith(
                borderRadius: BorderRadiusStyle.roundedBorder6,
              ),
              child: TabBar(
                controller: tabController,
                // onTap: (index) {
                //   tab = index;
                //   // Do something with the index
                //   print('Selected tab index: $tab');
                // },
                physics: const ClampingScrollPhysics(),
                unselectedLabelColor: Colors.black,
                labelColor: Theme.of(context).primaryColor,
                indicatorSize: TabBarIndicatorSize.tab,
                dividerColor: Colors.transparent,
                indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color: Colors.white),
                tabs: [
                  Tab(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 1.w),
                      child: const Text("Moble Money"),
                    ),
                  ),
                  Tab(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 1.w),
                      child: const Text("PayBill"),
                    ),
                  ),
                  Tab(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 1.w),
                      child: const Text("Till Number"),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 170.h,
              child: Column(
                children: [
                  Expanded(
                    child: TabBarView(
                      controller: tabController,
                      children: [
                        MobileMobile(context),
                        PayBill(context),
                        TillPage(context),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget MobileMobile(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PaymentChannelsBuilder2(
              selectedChannel: selectedChannel ?? "",
              onChange: (String? newlySelectedChannel) {
                setState(() {
                  selectedChannel = newlySelectedChannel;
                });
              }),
          Row(
            children: [
              Text(
                "Enter Beneficiary Phone Number",
                style: CustomTextStyles.titleSmallGray900,
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  final dynamic tooltip = _tooltipKey1.currentState;
                  tooltip.ensureTooltipVisible();
                },
                child: Tooltip(
                  key: _tooltipKey1,
                  message: KtStrings.benfChannel,
                  child: CustomImageView(
                    imagePath: AssetUrl.imgInbox,
                    height: 15.h,
                    width: 15.w,
                    margin: EdgeInsets.only(left: 3.w, top: 2.h, bottom: 3.h),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          CustomInternationalPhoneInput(
  onInputChanged: (num,
) {
              setState(() {
                setPhone = num.phoneNumber!;
              });
            },

          ),
        ],
      ),
    );
  }

  Widget PayBill(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              "Mpesa PayBill",
              style: CustomTextStyles.titleSmallGray900,
            ),
          ),
          CustomTextField(
            labelText: "PayBill number",
            controller: paybillController,
            hintText: "paybill",
            showNoKeyboard: true,
            allowAlphanumeric: true,
            isRequired: true,
            validator: (value) {
              if (value!.isEmpty) {
                return "Enter Paybill Number";
              } else if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value)) {
                return "Paybill number can only contain letters and numbers";
              } else {
                return null;
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              "Account number",
              style: CustomTextStyles.titleSmallGray900,
            ),
          ),
          CustomTextField(
            labelText: "Account number",
            controller: accountController,
            isRequired: true,
            hintText: "account number",
            allowAlphanumeric: true,
            validator: (value) {
              if (value!.isEmpty) {
                return "Enter Account Number";
              } else {
                return null;
              }
            },
          ),
        ],
      ),
    );
  }

  Widget TillPage(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 12.h,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10.0),
          child: Text(
            "Mpesa Till number",
            style: CustomTextStyles.titleSmallGray900,
          ),
        ),
        CustomTextField(
          labelText: "Till number",
          controller: tillController,
          showNoKeyboard: true,
          isRequired: true,
          hintText: "till number",
          validator: (value) {
            RegExp regex = RegExp(r'[a-zA-Z]');
            if (value!.isEmpty) {
              return "Enter Till Number";
            } else if (regex.hasMatch(value)) {
              return "Till number can not contain Alphabets";
            } else {
              return null;
            }
          },
        ),
      ],
    );
  }
}