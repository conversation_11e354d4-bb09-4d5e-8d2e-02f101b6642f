import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:flutter_quill_delta_from_html/flutter_quill_delta_from_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/models/chama/chamaDto.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/iswysiwyg.dart';

import '../../../../../models/chama/chama_model.dart';
import '../../../../../utils/utils_exports.dart';

class UpdateChama extends StatefulWidget {
  const UpdateChama({super.key});

  @override
  State<UpdateChama> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<UpdateChama> {
  final ChamaController _chamaController = Get.find<ChamaController>();
  final ChamaDataController _dataController = Get.put(ChamaDataController());
  TextEditingController chamaNameController = TextEditingController();
  TextEditingController descrController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController dateController = TextEditingController();
  TextEditingController timeController = TextEditingController();
  DateTime combinedDateTime = DateTime.now();

  TextEditingController amountController = TextEditingController();
  late q.QuillController _controller;
  String? selectedvalue;
  String? description;

  List<String> dropdownItems = [];
  TextEditingController freqcyController = TextEditingController();

  @override
  void initState() {
    setState(() {
      dropdownItems = _chamaController.frequencies
          .map((frequency) => frequency.frequency)
          .toList();
    });
    setvalues();
    super.initState();
  }

  void setvalues() {
    selectedvalue = _dataController.singleChamaDts.value.frequency;
    chamaNameController.text = _dataController.singleChamaDts.value.title ?? "";
    emailController.text = _dataController.singleChamaDts.value.email ?? "";
    amountController.text =
        _dataController.singleChamaDts.value.amount.toString();
    dateController.text = DateFormat.yMd().format(
        _dataController.singleChamaDts.value.nextOccurrence ?? DateTime.now());
    timeController.text =
        _dataController.singleChamaDts.value.nextOccurrence == null
            ? ""
            : DateFormat().add_jm().format(
                _dataController.singleChamaDts.value.nextOccurrence!.toLocal());

    // final String initialContent =
    //     _dataController.singleChamaDts.value.description ??
    //         ""; // Assuming content is a string
    // var myJSON = jsonDecode(initialContent);
    // _controller = q.QuillController(
    //   document: q.Document.fromJson(myJSON),
    //   selection: const TextSelection.collapsed(offset: 0),
    // );

    var document = q.Document.fromJson(isWysiwygFormat(
            _dataController.singleChamaDts.value.description ?? '')
        ? HtmlToDelta()
            .convert(_dataController.singleChamaDts.value.description ?? '')
            .toJson()
        : jsonDecode(_dataController.singleChamaDts.value.description ?? ''));
    var selection = const TextSelection.collapsed(offset: 0);
    _controller = q.QuillController(document: document, selection: selection);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar: buildAppBar(context),
        body: SingleChildScrollView(
          child: Column(
            children: [
              const RowAppBar(),
              Text(
                'update_chama_details'.tr,
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22),
              ),
              buildTextField(context),
              SizedBox(
                height: 20.h,
              ),
              buildSaveChanges(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildTextField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(15.0),
      child: Form(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'update_chama_name'.tr,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
            SizedBox(
              height: 5.h,
            ),
            CustomTextField(
              controller: chamaNameController,
              hintText: 'wedding_contribution_example'.tr,
              labelText: "",
              validator: (p0) {
                if (p0!.isEmpty) {
                  return 'filed_cannot_be_empty'.tr;
                } else if (p0.length < 5) {
                  return 'chama_name_length_validation'.tr;
                }
                return p0;
              },
            ),
            Text('chama_description'.tr,
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
            Container(
              decoration: BoxDecoration(
                color: Colors.blueAccent.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(
                  color: AppColors.blueButtonColor,
                  width: 1.0,
                ),
              ),
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  q.QuillSimpleToolbar(controller: _controller,
                    config: const q.QuillSimpleToolbarConfig(
                      multiRowsDisplay: false,
                      
                      
                    ),
                  ),
                  const SizedBox(height: 15),
                  q.QuillEditor.basic(controller: _controller,
                    config:  q.QuillEditorConfig(
                      placeholder: 'purpose_contribution_example'.tr,
                      
                      // readOnly: false,
                      autoFocus: false,
                      enableInteractiveSelection:
                          true, // Enable interactive selection to allow text editing

                      
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            Text(
              'chama_email'.tr,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
            SizedBox(
              height: 5.h,
            ),
            CustomTextField(
              controller: emailController,
              hintText: 'email_hint'.tr,
              labelText: 'email'.tr,
              validator: (p0) {
                if (p0!.isEmpty) {
                  return 'email_cannot_be_empty'.tr;
                } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                    .hasMatch(p0)) {
                  return 'enter_valid_email_address'.tr;
                }
                return null;
              },
            ),
            Text(
              'update_frequency'.tr,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
            SizedBox(
              height: 5.h,
            ),
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: 'frequency'.tr,
                fillColor: Colors.blueAccent.withOpacity(0.1),
              ),
              isExpanded: true,
              items: dropdownItems
                  .map(
                    (String item) => DropdownMenuItem<String>(
                      value: item,
                      child: Text(
                        item,
                        style: const TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ),
                  )
                  .toList(),
              value: selectedvalue,
              onChanged: (String? value) {
                setState(() {
                  selectedvalue = value;
                  freqcyController.text = value!;
                });
              },
            ),
            Text(
              description ?? "",
              style: const TextStyle(fontStyle: FontStyle.italic),
            ),
            SizedBox(
              height: 5.h,
            ),
            Text(
              'contribution_amount'.tr,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
            SizedBox(
              height: 5.h,
            ),
            CustomTextField(
              inputFormatters: <TextInputFormatter>[
                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              ],
              controller: amountController,
              hintText: 'amount_example'.tr,
              labelText: 'amount'.tr,
              validator: (p0) {
                if (p0!.isEmpty) {
                  return 'amount_required'.tr;
                }
                return null;
              },
            ),
            SizedBox(
              height: 8.h,
            ),
            Text(
              'update_chama_deadline'.tr,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
            SizedBox(
              height: 5.h,
            ),
            DatePick(
              date: dateController,
              time: timeController,
              // onDateTimeSelected: (DateTime) => _updateCombinedDateTime,
              combinedDateTime: combinedDateTime,
            ),
          ],
        ),
      ),
    );
  }

  Widget buildSaveChanges(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(right: 14.w),
      child: Align(
        alignment: Alignment.centerRight,
        child: Obx(() => CustomKtButton(
            isLoading: _chamaController.isUpdating.isTrue,
            onPress: () async {
              DateTime date = DateFormat.yMd().parse(dateController.text);

              TimeOfDay time = TimeOfDay.fromDateTime(
                  DateFormat.Hm().parse(timeController.text));

              DateTime combinedDateTime = DateTime(
                date.year,
                date.month,
                date.day,
                time.hour,
                time.minute,
              );

              final updateDto = UpdateDto(
                id: _dataController.singleChamaDts.value.id ?? 1,
                title: chamaNameController.text,
                description: quilltoHtml(_controller),
                email: emailController.text,
                frequency: selectedvalue ?? "",
                amount: double.parse(amountController.text),
                nextOccurrence: combinedDateTime.toUtc(),
              );
              final res =
                  await _chamaController.UpdateChama(updateDto: updateDto);

              if (res) {
                // Refresh current chama data
                var resp = await _chamaController.getChamaDetails(
                    chamaId: _dataController.singleChamaDts.value.id ?? 1);

                if (resp) {
                  var chamaDataController = Get.put(ChamaDataController());
                  chamaDataController.singleChamaDts.value = Chama();
                  chamaDataController.singleChamaDts.value =
                      _chamaController.chamaDetails.value;
                }
                // Refresh user chamas list
                await _chamaController.getUserChamas();
                Get.offNamed(NavRoutes.singleChama);
                Snack.show(res, _chamaController.apiMessage.string);
              } else {
                Snack.show(res, _chamaController.apiMessage.string);
              }
            },
            width: 200,
            height: 60,
            btnText: 'save_changes'.tr)),
      ),
    );
  }
}
