// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/bulksms_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/utils/size_config.dart';

import '../../../../../utils/utils_exports.dart';
import 'widgets/sms_item.dart';

class SingleSmsScreen extends StatefulWidget {
  final dynamic messageId;
  const SingleSmsScreen({super.key, this.messageId});

  @override
  State<SingleSmsScreen> createState() => _SingleSmsScreenState();
}

class _SingleSmsScreenState extends State<SingleSmsScreen> {
  TextEditingController messageController = TextEditingController();
  final MessageController _message = Get.put(MessageController());
  final BulkSMSController _bulkSMSController = Get.put(BulkSMSController());
  final _userKittyController = Get.put(UserKittyController());

  GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  @override
  void initState() {
    _loadmsg();
    messageController.text = _message.reSmessage.value;

    super.initState();
  }

  Future<void> _loadmsg() async {
    _bulkSMSController.isMsgloading(true);
    try {
      await _bulkSMSController.getMsg(msgId: _message.msgId.value);
    } catch (e) {
      throw e;
    }
    _bulkSMSController.isMsgloading(false);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        //appBar: buildAppBar(context),
        body: SizedBox(
          width: double.maxFinite,
          child: Column(
            children: [
              RowAppBar(),
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 30.w),
                    child: Stack(
                      alignment: Alignment.bottomCenter,
                      children: [
                        Align(
                          alignment: Alignment.center,
                          child: Padding(
                            padding: EdgeInsets.only(left: 2.w),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Align(
                                    alignment: Alignment.center,
                                    child: GetX(
                                      builder: (BulkSMSController
                                          bulkSMSController) {
                                        if (bulkSMSController
                                            .isMsgloading.isTrue) {
                                          return Text(
                                            "loading".tr,
                                            style: const TextStyle(
                                                color: Color.fromARGB(
                                                    255, 5, 78, 138),
                                                fontSize: 15,
                                                fontStyle: FontStyle.italic),
                                          );
                                        }
                                        return Text(
                                            DateFormat('MMM dd, yyyy').format(
                                                bulkSMSController.msg.isNotEmpty
                                                    ? bulkSMSController.msg.first.createdAt?.toLocal() ?? DateTime.now()
                                                    : DateTime.now()),
                                            style: CustomTextStyles
                                                .titleLargeGray900);
                                      },
                                    )),
                                SizedBox(height: 12.h),
                                Text("Message sent",
                                    style:
                                        CustomTextStyles.titleMediumBlack900),
                                SizedBox(height: 5.h),
                                _buildMessage(context),
                                SizedBox(height: 12.h),
                                Align(
                                  alignment: Alignment.center,
                                  child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 23.w),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(children: [
                                          Text("recipients".tr,
                                              style: theme.textTheme.bodySmall),
                                          GetX(
                                            builder: (BulkSMSController
                                                bulkSMSController) {
                                              if (bulkSMSController
                                                  .isMsgloading.isTrue) {
                                                return Text(
                                                  "loading".tr,
                                                  style: const TextStyle(
                                                      color: Color.fromARGB(
                                                          255, 5, 78, 138),
                                                      fontSize: 15,
                                                      fontStyle:
                                                          FontStyle.italic),
                                                );
                                              }
                                              return Text(
                                                "${bulkSMSController.msgresults.value?.total}",
                                                style: const TextStyle(
                                                    color: Color.fromARGB(
                                                        255, 5, 78, 138),
                                                    fontSize: 25),
                                              );
                                            },
                                          ),
                                        ]),
                                        Column(
                                          children: [
                                            Text("my_wallet".tr,
                                                style:
                                                    theme.textTheme.bodySmall),
                                            Text(
                                              FormattedCurrency
                                                  .getFormattedCurrency(
                                                double.parse(
                                                  _userKittyController
                                                          .getLocalUser()
                                                          ?.balance
                                                          ?.toStringAsFixed(
                                                              2) ??
                                                      "",
                                                ),
                                              ),
                                              style: const TextStyle(
                                                  color: Color.fromARGB(
                                                      255, 5, 78, 138),
                                                  fontSize: 25),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                SizedBox(height: 19.h),
                                Text("Message sent",
                                    style:
                                        CustomTextStyles.titleMediumBlack900),
                                SizedBox(height: 5.h),
                                Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 15.w, vertical: 16.h),
                                    decoration: AppDecoration.outlineGray
                                        .copyWith(
                                            borderRadius: BorderRadiusStyle
                                                .roundedBorder8),
                                    child: _buildSingleBulkSms(context))
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildMessage(BuildContext context) {
    return TextFormField(
      readOnly: true,
      controller: messageController,
      // hintStyle: CustomTextStyles.titleSmallGray900,
      textInputAction: TextInputAction.done,
      maxLines: 4,
      // contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 18.h),
    );
  }

  /// Section Widget
  Widget _buildSingleBulkSms(BuildContext context) {
    return GetX(
      builder: (BulkSMSController bulkSMSController) {
        if (bulkSMSController.isMsgloading.isTrue) {
          return SizedBox(
            height: SizeConfig.screenHeight * .33,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitDualRing(
                    color: ColorUtil.blueColor,
                    lineWidth: 4.sp,
                    size: 40.0.sp,
                  ),
                  const Text(
                    "loading..",
                    style: TextStyle(
                      color: Colors.white,
                      fontStyle: FontStyle.italic,
                    ),
                  )
                ],
              ),
            ),
          );
        }
        if (bulkSMSController.msg.isEmpty) {
          return Center(
            child: Text(
              "Error while getting recipients",
              style: TextStyle(
                color: Colors.red, // Adjust color as needed
                fontSize: 16.0, // Adjust font size as needed
              ),
            ),
          );
        }
        if (bulkSMSController.msg.isNotEmpty) {
          return Column(
            children: [
              ListView.separated(
                controller: bulkSMSController.controller,
                physics: BouncingScrollPhysics(),
                shrinkWrap: true,
                separatorBuilder: (context, index) {
                  return Padding(
                    padding: EdgeInsets.symmetric(vertical: 5.0.h),
                    child: SizedBox(
                      width: 334.w,
                      child: Divider(
                        height: 1.h,
                        thickness: 1.h,
                        color: appTheme.gray900.withOpacity(0.1),
                      ),
                    ),
                  );
                },
                itemCount: bulkSMSController.msg.length,
                itemBuilder: (context, index) {
                  var item = bulkSMSController.msg[index];
                  return SinglesmsItem(
                    index: index,
                    item: item,
                  );
                },
              ),
              if (bulkSMSController.loadingMore.isTrue)
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: Column(
                    children: [
                      SpinKitDualRing(
                        color: ColorUtil.blueColor,
                        lineWidth: 4.sp,
                        size: 40.0.sp,
                      ),
                      const Text(
                        "loading..",
                        style: TextStyle(
                          color: Colors.white,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          );
        }
        return Center(
          child: Text(
            "Found some issue",
            style: TextStyle(
              color: Colors.red, // Adjust color as needed
              fontSize: 16.0, // Adjust font size as needed
            ),
          ),
        );
      },
    );
  }

  onTapArrowLeft(BuildContext context) {
    Navigator.pop(context);
  }
}
