import 'dart:convert';
import 'package:get/get.dart';

CreateKitPayload createKitPayloadFromJson(String str) =>
    CreateKitPayload.fromJson(json.decode(str));

String createKitPayloadToJson(CreateKitPayload data) =>
    json.encode(data.toJson());

class CreateKitPayload {
  int? id;
  String? title;
  String? description;
  String? beneficiaryAccount;
  String? beneficiaryAccountRef;

  String? beneficiaryChannel;
  String? beneficiaryPhoneNumber;
  int? settlementType;

  DateTime? endDate;
  int? limit;
  int? refererMerchantCode;
  String? whatsappLink;
  String? phoneNumber;
  String? userId;
  List<Media>? media;
  num? target; 

  List<int>? categories;


  CreateKitPayload({
    this.id,
    this.title,
    this.description,
    this.beneficiaryAccount,
    this.beneficiaryAccountRef,
    this.beneficiaryChannel,
    this.beneficiaryPhoneNumber,
    this.settlementType,
    this.endDate,
    this.limit,
    this.refererMerchantCode,
    this.whatsappLink,
    this.phoneNumber,
    this.userId,
    this.media,
    this.target, 
    this.categories,
  });

  factory CreateKitPayload.fromJson(Map<String, dynamic> json) =>
      CreateKitPayload(
        id: json["id"],
        title: json["title"],
        description: json["description"],
        beneficiaryAccount: json["beneficiary_account"],
        beneficiaryAccountRef: json["beneficiary_account_ref"],
        beneficiaryChannel: json["beneficiary_channel"],
        beneficiaryPhoneNumber: json["beneficiary_phone_number"],
        settlementType: json["settlement_type"],
        endDate:
            json["end_date"] == null ? null : DateTime.parse(json["end_date"]),
        limit: json["limit"],
        refererMerchantCode: json["referer_merchant_code"],
        whatsappLink: json["whatsapp_link"],
        phoneNumber: json["phone_number"] ?? "",
        userId: json["UserID"],
        target: json["limit"],
       
        media: json["media"] == null
            ? []
            : List<Media>.from(json["media"]!.map((x) => Media.fromJson(x))),
        categories: json["categories"] == null
            ? []
            : List<int>.from(json["categories"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "description": description,
        "beneficiary_account": beneficiaryAccount,
        "beneficiary_account_ref": beneficiaryAccountRef,
        "beneficiary_channel": beneficiaryChannel,
        "beneficiary_phone_number": beneficiaryPhoneNumber,
        "end_date": "${endDate?.toIso8601String().removeAllWhitespace}",
        "limit": limit,
        "referer_merchant_code": refererMerchantCode,
        "settlement_type": settlementType,
        "whatsapp_link": whatsappLink,
        "phone_number": phoneNumber,
        "UserID": userId, 
        "media": media == null
            ? []
            : List<dynamic>.from(media!.map((x) => x.toJson())),
        "categories": categories,
      };
}

class Media {
  String? url;
  String? type;

  Media({
    this.url,
    this.type,
  });

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        url: json["url"],
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "url": url,
        "type": type,
      };
}