// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/config.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/chama/widgets/create_tabs.dart';
import '../../../../utils/utils_exports.dart';

class HomeServices extends StatelessWidget {
  ConfigController configController = Get.put(ConfigController());
  HomeServices({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 8.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 2.w),
          child: Wrap(
            spacing: 5,
            runSpacing: 5,
            children: [
              BuildCard(
                edit: AssetUrl.imgPlus,
                color: AppColors.blueButtonColor,
                resources: "create".tr,
                onTap: () {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: true,
                    barrierLabel: 'close'.tr,
                    barrierColor: Colors.black.withOpacity(0.5),
                    transitionDuration: const Duration(milliseconds: 200),
                    pageBuilder: (BuildContext context, Animation animation,
                        Animation secondaryAnimation) {
                      return const CreateTabs();
                    },
                    transitionBuilder: (BuildContext context,
                        Animation<double> animation,
                        Animation<double> secondaryAnimation,
                        Widget child) {
                      return ScaleTransition(
                        scale: animation,
                        child: child,
                      );
                    },
                  );
                },
              ),
              BuildCard(
                edit: AssetUrl.handCoins,
                resources: "contribute".tr,
                onTap: () {
                  //Get.toNamed( NavRoutes.urlScreen);
                  Get.toNamed(NavRoutes.urlScreen);
                },
              ),
              BuildCard(
                  edit: AssetUrl.support,
                  resources: "chat_with_us".tr,
                  onTap: () {
                    configController.getConfig2();
                    Get.toNamed(NavRoutes.tawk);
                  }),
              BuildCard(
                edit: AssetUrl.message,
                resources: "bulk_sms".tr,
                onTap: () {
                  Get.toNamed(NavRoutes.mainbulksms);
                },
              ),
              BuildCard(
                edit: AssetUrl.imgNavChama,
                resources: "referrals".tr,
                onTap: () {
                  Get.toNamed(NavRoutes.referrals);
                },
              ),
              // BuildCard(
              //   edit: AssetUrl.handCoins,
              //   resources: "Upload",
              //   onTap: () {
              //     Get.to(() => UploadContacts());
              //   },
              // ),
            ],
          ),
        ),
      ],
    );
  }
}
