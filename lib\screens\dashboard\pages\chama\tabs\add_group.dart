import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';

import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/custom_button.dart';
import 'package:onekitty/utils/whatsapp_validator.dart';

class AddGroup extends StatefulWidget {
  const AddGroup({super.key});

  @override
  State<AddGroup> createState() => _AddGroupState();
}

class _AddGroupState extends State<AddGroup> {
  final ChamaController _chamaController = Get.find<ChamaController>();
  final ChamaDataController _dataController = Get.put(ChamaDataController());
  TextEditingController linkController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  final box = GetStorage();
  bool isLoading = false;
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Container(
          margin: const EdgeInsets.all(20),
          child: Form(
            key: formKey,
            child: Column(
              children: [
                Text(
                  'connect_whatsapp_group'.tr,
                  style:
                      context.titleText?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(_dataController.singleChamaDts.value.title ?? ""),
                const SizedBox(
                  height: 30,
                ),
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    'whatsapp_group_link'.tr,
                    style: context.titleText,
                  ),
                ),
                CustomTextField(
                  hintText:
                      "e.g https://chat.whatsapp.com/K8jDByxIyv4HLjCn32bEp0",
                  labelText: 'enter_whatsapp_link'.tr,
                  controller: linkController,
                  validator: (p0) {
                    if (p0!.isEmpty) {
                      return 'provide_a_link'.tr;
                    } else if (!WhatsAppValidator.isValidWhatsAppLink(p0)) {
                      return WhatsAppValidator.getValidationErrorMessage();
                    }
                    return null;
                  },
                ),
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    'chama_email'.tr,
                    style: context.titleText,
                  ),
                ),
                CustomTextField(
                  controller: emailController,
                  hintText: 'email_hint'.tr,
                  labelText: 'enter_your_chama_email'.tr,
                  validator: (p0) {
                    if (p0!.isEmpty) {
                      return 'email_cannot_be_empty'.tr;
                    } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                        .hasMatch(p0)) {
                      return 'enter_valid_email_address'.tr;
                    }
                    return null;
                  },
                ),
                const SizedBox(
                  height: 20,
                ),
                SizedBox(
                  height: 50,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      OutlinedButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 12.0),
                            child: Text(
                              'back'.tr,
                              style: context.dividerTextLarge
                                  ?.copyWith(color: AppColors.primary),
                            ),
                          )),
                      Obx(() => CustomKtButton(
                            width: 100.w,
                            isLoading: _chamaController.isAddingG.isTrue,
                            onPress: () async {
                              if (formKey.currentState!.validate()) {
                                final res = await _chamaController.addGroup(
                                  chId:
                                      _dataController.singleChamaDts.value.id ??
                                          1,
                                  link: linkController.text.trim(),
                                  email: emailController.text,
                                );
                                if (res) {
                                  ToastUtils.showSuccessToast(
                                      context,
                                      _chamaController.apiMessage.string,
                                      'success'.tr);
                                  Get.offAndToNamed(NavRoutes.singleChama);
                                } else {
                                  ToastUtils.showErrorToast(
                                      context,
                                      _chamaController.apiMessage.string,
                                      'error'.tr);
                                }
                              }
                            },
                            btnText: 'submit'.tr,
                          ))
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
