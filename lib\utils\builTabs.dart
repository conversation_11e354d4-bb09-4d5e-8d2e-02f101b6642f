import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/configs/payment_channels.dart';
import 'package:onekitty/helpers/colors.dart';

Widget buildTabs(TabController tabController, context) {
  final paymentChannel = Get.isRegistered<PaymentChannel>()
      ? Get.find<PaymentChannel>()
      : Get.put(PaymentChannel());
  return Container(
      padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 5),
      decoration: BoxDecoration(
        color: AppColors.slate,
        borderRadius: BorderRadius.circular(15),
      ),
      child: TabBar(
          controller: tabController,
          physics: const ClampingScrollPhysics(),
          padding: const EdgeInsets.only(left: 5, right: 5),
          unselectedLabelColor: Colors.black,
          labelColor: Theme.of(context).primaryColor,
          indicatorSize: TabBarIndicatorSize.tab,
          dividerColor: Colors.transparent,
          indicator: BoxDecoration(
              borderRadius: BorderRadius.circular(12), color: Colors.white),
          tabs: List.generate(paymentChannel.paymentGateways().length, (index) {
            return Tab(
              child: AutoSizeText(paymentChannel.paymentGateways()[index], maxLines: 1, minFontSize: 4),
            );
          })));
}
