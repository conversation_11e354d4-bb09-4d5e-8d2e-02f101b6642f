import 'dart:convert';

TransferRequest transferRequestFromJson(String str) =>
    TransferRequest.fromJson(json.decode(str));

String transferRequestToJson(TransferRequest data) =>
    json.encode(data.toJson());

class TransferRequest {
  int? amount;
  int? userId;
  int? chamaId;
  int? kittyId; // For event transfers
  int? memberId;
  int? channelCode;
  String? recipientAccountNumber;
  String? recipientAccountRef;
  String? reason;
  String? transferMode;
  String? latitude;
  String? longitude;
  String? deviceId;
  String? deviceModel;
  bool? isPenaltyKitty;

  TransferRequest({
    this.amount,
    this.userId,
    this.chamaId,
    this.kittyId,
    this.memberId,
    this.channelCode,
    this.recipientAccountNumber,
    this.recipientAccountRef,
    this.reason,
    this.transferMode,
    this.latitude,
    this.longitude,
    this.deviceId,
    this.deviceModel,
    this.isPenaltyKitty,
  });

  factory TransferRequest.fromJson(Map<String, dynamic> json) =>
      TransferRequest(
        amount: json["amount"],
        userId: json["user_id"],
        chamaId: json["chama_id"],
        kittyId: json["kitty_id"],
        memberId: json["member_id"],
        channelCode: json["channel_code"],
        recipientAccountNumber: json["recipient_account_number"],
        recipientAccountRef: json["recipient_account_ref"],
        reason: json["reason"],
        transferMode: json["transfer_mode"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        deviceId: json["device_id"],
        deviceModel: json["device_model"],
        isPenaltyKitty: json["is_penalty_kitty"],
      );

  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = {
      "amount": amount,
      "user_id": userId,
      "channel_code": channelCode,
      "recipient_account_number": recipientAccountNumber,
      "reason": reason,
      "transfer_mode": transferMode,
      "latitude": latitude,
      "longitude": longitude,
      "device_id": deviceId,
      "device_model": deviceModel,
    };
    
    // Add kittyId for event transfers
    if (kittyId != null) {
      json["kitty_id"] = kittyId;
    }
    
    // Add chama-specific fields
    if (chamaId != null) {
      json["chama_id"] = chamaId;
    }
    if (memberId != null) {
      json["member_id"] = memberId;
    }
    if (isPenaltyKitty != null) {
      json["is_penalty_kitty"] = isPenaltyKitty;
    }
    
    // Add recipient_account_ref only if not null
    if (recipientAccountRef != null) {
      json["recipient_account_ref"] = recipientAccountRef;
    }
    
    return json;
  }
}
