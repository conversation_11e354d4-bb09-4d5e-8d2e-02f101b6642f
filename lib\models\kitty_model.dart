import 'package:onekitty/models/kitty/kitty_categories_model.dart';

class CreateKittyResponse {
  Data? data;
  String? message;
  SocialMesages? socialMesages;
  bool? status;
  dynamic whatsappData;
  String? whatsappMessage;
  bool? whatsappStatus;

  CreateKittyResponse(
      {this.data,
      this.message,
      this.socialMesages,
      this.status,
      this.whatsappData,
      this.whatsappMessage,
      this.whatsappStatus});

  CreateKittyResponse.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    message = json['message'];
    socialMesages = json['social_mesages'] != null
        ? SocialMesages.fromJson(json['social_mesages'])
        : null;
    status = json['status'];
    whatsappData = json['whatsapp_data'];
    whatsappMessage = json['whatsapp_message'];
    whatsappStatus = json['whatsapp_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['message'] = message;
    if (socialMesages != null) {
      data['social_mesages'] = socialMesages!.toJson();
    }
    data['status'] = status;
    data['whatsapp_data'] = whatsappData;
    data['whatsapp_message'] = whatsappMessage;
    data['whatsapp_status'] = whatsappStatus;
    return data;
  }
}

class Data {
  Kitty? kitty;
  Merchant? merchant;
  String? url;

  Data({this.kitty, this.merchant, this.url});

  Data.fromJson(Map<String, dynamic> json) {
    kitty = json['kitty'] != null ? Kitty.fromJson(json['kitty']) : null;
    merchant =
        json['merchant'] != null ? Merchant.fromJson(json['merchant']) : null;
    url = json['url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (kitty != null) {
      data['kitty'] = kitty!.toJson();
    }
    if (merchant != null) {
      data['merchant'] = merchant!.toJson();
    }
    data['url'] = url;
    return data;
  }
}

class Kitty {
  int? iD;
  DateTime? createdAt;
  DateTime? updatedAt;
  DateTime? deletedAt;
  String? title;
  String? description;
  String? beneficiaryAccount;
  String? beneficiaryChannel;
  String? beneficiaryPhoneNumber;
  DateTime? endDate;
  num? balance;
  int? limit;
  int? refererMerchantCode;
  String? phoneNumber;
  int? settlementType;
  List<KittyCategoriesModel>? categories;
  String? username;
  String? beneficiaryAccountRef;
  double? percentage;
  String? identifier;
  int? categoryId;
  int? status;
  String? bennefAccRef;
  int? kittyType;
  List<KittyMediaModel>? media;
  dynamic availableBalance;
  int? userId;

  Kitty(
      {this.iD,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.title,
      this.description,
      this.beneficiaryAccount,
      this.beneficiaryChannel,
      this.beneficiaryPhoneNumber,
      this.endDate,
      this.balance,
      this.limit,
      this.refererMerchantCode,
      this.phoneNumber,
      this.settlementType,
      this.categories,
      this.username,
      this.beneficiaryAccountRef,
      this.percentage,
      this.identifier,
      this.categoryId,
      this.status,
      this.bennefAccRef,
      this.kittyType,
      this.media,
      this.availableBalance,
      this.userId});

  Kitty.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    createdAt =
        json['CreatedAt'] != null ? DateTime.tryParse(json['CreatedAt']) : null;
    updatedAt =
        json['UpdatedAt'] != null ? DateTime.tryParse(json['UpdatedAt']) : null;
    deletedAt = json['DeletedAt'];
    title = json['title'];
    description = json['description'];
    beneficiaryAccount = json['beneficiary_account'];
    beneficiaryChannel = json['beneficiary_channel'];
    beneficiaryPhoneNumber = json['beneficiary_phone_number'];
    endDate =
        json["end_date"] == null ? null : DateTime.parse(json["end_date"]);
    balance = json['balance'] ?? 0;
    limit = json['limit'];
    refererMerchantCode = json['referer_merchant_code'];
    phoneNumber = json['phone_number'];
    settlementType = json['settlement_type'];

    categories = json["categories_response"] == null
        ? []
        : List<KittyCategoriesModel>.from(json["categories_response"]
            .map((x) => KittyCategoriesModel.fromJson(x))).toList();
    username = json['username'];
    beneficiaryAccountRef = json['beneficiary_account_ref'];
    percentage = json['percentage'];
    identifier = json['identifier'];
    status = json['status'];
    kittyType = json['kitty_type'];
    media = json["media"] == null
        ? []
        : List<KittyMediaModel>.from(
            json["media"].map((x) => KittyMediaModel.fromJson(x))).toList();
    availableBalance = json['available_balance'];
    userId = json['user_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ID'] = iD;
    data['CreatedAt'] = createdAt;
    data['UpdatedAt'] = updatedAt;
    data['DeletedAt'] = deletedAt;
    data['title'] = title;
    data['description'] = description;
    data['beneficiary_account'] = beneficiaryAccount;
    data['beneficiary_channel'] = beneficiaryChannel;
    data['beneficiary_phone_number'] = beneficiaryPhoneNumber;
    data['end_date'] = endDate;
    data['balance'] = balance;
    data['limit'] = limit;
    data['referer_merchant_code'] = refererMerchantCode;
    data['phone_number'] = phoneNumber;
    data['settlement_type'] = settlementType;
    data['categories'] = categories;
    data['username'] = username;
    data['beneficiary_account_ref'] = beneficiaryAccountRef;
    data['percentage'] = percentage;
    data['identifier'] = identifier;
    data['category_id'] = categoryId;
    data['status'] = status;
    data['bennef_acc_ref'] = bennefAccRef;
    data['kitty_type'] = kittyType;
    data['media'] = media?.map((e) => e.toJson()) ?? [];

    return data;
  }
}

class Merchant {
  int? iD;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  int? userID;
  String? merchantName;
  int? merchantCode;
  num? merchantPercent;

  Merchant(
      {this.iD,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.userID,
      this.merchantName,
      this.merchantCode,
      this.merchantPercent});

  Merchant.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    createdAt = json['CreatedAt'];
    updatedAt = json['UpdatedAt'];
    deletedAt = json['DeletedAt'];
    userID = json['UserID'];
    merchantName = json['merchant_name'];
    merchantCode = json['merchant_code'];
    merchantPercent = json['merchant_percent'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ID'] = iD;
    data['CreatedAt'] = createdAt;
    data['UpdatedAt'] = updatedAt;
    data['DeletedAt'] = deletedAt;
    data['UserID'] = userID;
    data['merchant_name'] = merchantName;
    data['merchant_code'] = merchantCode;
    data['merchant_percent'] = merchantPercent;
    return data;
  }
}

class SocialMesages {
  int? iD;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? facebook;
  String? tiktok;
  String? instagram;
  String? youtube;
  String? twitter;

  SocialMesages(
      {this.iD,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.facebook,
      this.tiktok,
      this.instagram,
      this.youtube,
      this.twitter});

  SocialMesages.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    createdAt = json['CreatedAt'];
    updatedAt = json['UpdatedAt'];
    deletedAt = json['DeletedAt'];
    facebook = json['facebook'];
    tiktok = json['tiktok'];
    instagram = json['instagram'];
    youtube = json['youtube'];
    twitter = json['twitter'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ID'] = iD;
    data['CreatedAt'] = createdAt;
    data['UpdatedAt'] = updatedAt;
    data['DeletedAt'] = deletedAt;
    data['facebook'] = facebook;
    data['tiktok'] = tiktok;
    data['instagram'] = instagram;
    data['youtube'] = youtube;
    data['twitter'] = twitter;
    return data;
  }
}

class KittyMediaModel {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final String? url;
  final String title;
  final String type;
  final String status;
  final String? category;
  final int? incidentReportId;
  final int? kittyId;

  KittyMediaModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.url,
    required this.title,
    required this.type,
    required this.status,
    this.category,
    this.incidentReportId,
    this.kittyId,
  });

  factory KittyMediaModel.fromJson(Map<String, dynamic> json) {
    return KittyMediaModel(
      id: json['ID'] ?? 0,
      createdAt: DateTime.tryParse(json['CreatedAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['UpdatedAt'] ?? '') ?? DateTime.now(),
      deletedAt: json['DeletedAt'] != null
          ? DateTime.tryParse(json['DeletedAt'])
          : null,
      url: json['url'],
      title: json['title'] ?? '',
      type: json['type'] ?? '',
      status: json['status'] ?? '',
      category: json['category'] ?? '',
      incidentReportId: json['incident_report_id'] ,
      kittyId: json['kitty_id'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'ID': id,
      'CreatedAt': createdAt?.toUtc().toIso8601String(),
      'UpdatedAt': updatedAt?.toUtc().toIso8601String(),
      'DeletedAt': deletedAt?.toUtc().toIso8601String(),
      'url': url,
      'title': title,
      'type': type,
      'status': status,
      'category': category,
      'incident_report_id': incidentReportId,
      'kitty_id': kittyId,
    };
  }
}
