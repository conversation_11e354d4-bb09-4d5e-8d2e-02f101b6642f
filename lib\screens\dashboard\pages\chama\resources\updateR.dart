import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/size_config.dart';

import '../../../../../models/chama/chamaDto.dart';
import '../../../../../utils/utils_exports.dart';

class UpdateResource extends StatefulWidget {
  const UpdateResource({super.key});

  @override
  State<UpdateResource> createState() => _UploadResourceState();
}

class _UploadResourceState extends State<UpdateResource> {
  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController _dataController = Get.put(ChamaDataController());
  TextEditingController titleController = TextEditingController();
  TextEditingController descrController = TextEditingController();

  @override
  initState() {
    titleController.text = _dataController.singleRs.value.title ?? "";
    descrController.text = _dataController.singleRs.value.description ?? "";
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar: buildAppBar(context),
        body: Padding(
          padding: EdgeInsets.only(left: 15.0.w, right: 15.w),
          child: Column(
            children: [
              SizedBox(
                height: 12.h,
              ),
              const RowAppBar(),
              Text(
                "update_documents".tr,
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22),
              ),
              SizedBox(
                height: 5.h,
              ),
              Container(
                width: 350.w,
                margin: EdgeInsets.symmetric(horizontal: 10.w),
                child: Text(
                  "share_documents_description".tr,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: CustomTextStyles.bodyLargeOnPrimaryContainer,
                ),
              ),
              SizedBox(
                height: 20.h,
              ),
              _buildTextField(context),
              SizedBox(
                height: 20.h,
              ),
              _buildButton(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildButton(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Obx(
        () => CustomKtButton(
          isLoading: chamaController.isUploading.isTrue,
          onPress: () async {
            onSubmit(context);
          },
          width: SizeConfig.screenWidth,
          height: 60,
          btnText: "update".tr,
        ),
      ),
    );
  }

  Widget _buildTextField(BuildContext context) {
    return Form(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("document_title".tr,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          CustomTextField(
            controller: titleController,
            labelText: '',
            validator: (p0) {
              if (p0!.isEmpty) {
                return "field_cannot_be_empty".tr;
              } else if (p0.length < 5) {
                return "document_title_length_validation".tr;
              }
              return p0;
            },
          ),
          Text("enter_document_description".tr,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          CustomTextField(
            controller: descrController,
            labelText: '',
            validator: (p0) {
              if (p0!.isEmpty) {
                return "field_cannot_be_empty".tr;
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Future<void> onSubmit(
    BuildContext context,
  ) async {
    try {
      final upload = UploadRDto(
          id: _dataController.singleRs.value.id,
          title: titleController.text,
          description: descrController.text,
          chamaId: _dataController.singleChamaDts.value.id ?? 0,
          type: _dataController.singleRs.value.type ?? '');

      final res = await chamaController.updateResource(uploadDto: upload);
      if (res) {
        ToastUtils.showSuccessToast(
            context, chamaController.apiMessage.string, "success".tr);
        chamaController.getResources(
          chamaId: _dataController.singleChamaDts.value.id ?? 0,
        );
        Get.back();
      } else {
        ToastUtils.showErrorToast(
            context, chamaController.apiMessage.string, "error".tr);
      }
    } catch (e) {
      ToastUtils.showErrorToast(context, "file_upload_failed".tr.replaceAll( "{error}", e.toString()),"$e" );
    }
  }
}
