class EventMedia {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final int? eventId;
  final String? url;
  final String? title;
  final String? description;
  final String? type;
  final String? category;

  EventMedia({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.eventId,
    this.url,
    this.title,
    this.description,
    this.type,
    this.category,
  });

  factory EventMedia.fromJson(Map<String, dynamic> json) => EventMedia(
        id: json["ID"],
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"] != null
            ? DateTime.parse(json["DeletedAt"])
            : null,
        eventId: json["event_id"],
        url: json["url"],
        title: json["title"],
        description: json["description"],
        type: json["type"],
        category: json["category"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toUtc().toIso8601String(),
        "UpdatedAt": updatedAt?.toUtc().toIso8601String(),
        "DeletedAt": deletedAt,
        "event_id": eventId,
        "url": url,
        "title": title,
        "description": description,
        "type": type,
        "category": category,
      };
}

/// Model for API payload when uploading/updating event media
class EventMediaPayload {
  final String media;
  final String type;
  final String url;
  final int eventId;
  final String title;
  final String description;
  final String category;

  const EventMediaPayload({
    required this.media,
    required this.type,
    required this.url,
    required this.eventId,
    required this.title,
    required this.description,
    required this.category,
  });

  /// Create payload from EventMedia instance
  factory EventMediaPayload.fromEventMedia(EventMedia eventMedia, int eventId) {
    return EventMediaPayload(
      media: eventMedia.url ?? '',
      type: eventMedia.type ?? 'image',
      url: eventMedia.url ?? '',
      eventId: eventId,
      title: eventMedia.title ?? "event_media$eventId",
      description: eventMedia.description ?? '',
      category: eventMedia.category ?? 'EventMedia',
    );
  }

  /// Create payload with custom values
  factory EventMediaPayload.create({
    required String url,
    required int eventId,
    String type = 'image',
    String? title,
    String description = '',
    String category = 'EventMedia',
  }) {
    return EventMediaPayload(
      media: url,
      type: type,
      url: url,
      eventId: eventId,
      title: title ?? "event_media$eventId",
      description: description,
      category: category,
    );
  }

  /// Convert to JSON for API requests
  Map<String, dynamic> toJson() => {
        "media": media,
        "type": type,
        "url": url,
        "event_id": eventId,
        "title": title,
        "description": description,
        "category": category,
      };

  /// Create from JSON response
  factory EventMediaPayload.fromJson(Map<String, dynamic> json) {
    return EventMediaPayload(
      media: json["media"] ?? json["url"] ?? '',
      type: json["type"] ?? 'image',
      url: json["url"] ?? json["media"] ?? '',
      eventId: json["event_id"] ?? 0,
      title: json["title"] ?? '',
      description: json["description"] ?? '',
      category: json["category"] ?? 'EventMedia',
    );
  }

  /// Validate payload data
  bool get isValid =>
      media.isNotEmpty &&
      url.isNotEmpty &&
      eventId > 0 &&
      type.isNotEmpty;

  /// Create a copy with updated values
  EventMediaPayload copyWith({
    String? media,
    String? type,
    String? url,
    int? eventId,
    String? title,
    String? description,
    String? category,
  }) {
    return EventMediaPayload(
      media: media ?? this.media,
      type: type ?? this.type,
      url: url ?? this.url,
      eventId: eventId ?? this.eventId,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EventMediaPayload &&
          runtimeType == other.runtimeType &&
          media == other.media &&
          url == other.url &&
          eventId == other.eventId;

  @override
  int get hashCode => media.hashCode ^ url.hashCode ^ eventId.hashCode;

  @override
  String toString() {
    return 'EventMediaPayload{media: $media, type: $type, url: $url, eventId: $eventId, title: $title, description: $description, category: $category}';
  }
}
