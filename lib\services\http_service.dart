import 'dart:io';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'custom_logger.dart';
import 'package:firebase_core/firebase_core.dart'; 
enum Method { POST, GET, PUT, DELETE, PATCH }

class HttpService {
  HttpService();
// TODO change to live later
  static  String? baseUrl = ApiUrls.BASE_URL_LIVE;
  static final box = GetStorage();
  Dio? dio = Dio(
    BaseOptions(
      baseUrl: baseUrl ?? "",
      receiveDataWhenStatusError: false,
      followRedirects: true,
      receiveTimeout: const Duration(minutes: 2),
      sendTimeout: const Duration(minutes: 1),
      validateStatus: (status) {
        return status! < 500;
      },
    ),
  );

  ///Always use this method to initialize the dio client
  Dio initializeDio() {
    dio?.interceptors.add(
      InterceptorsWrapper(
        onRequest:
            (RequestOptions options, RequestInterceptorHandler handler) async {
          options.headers = await header();
          logger.v("REQUEST[${options.method}] => PATH: ${options.uri}"
              "=> REQUEST VALUE S: ${options.data} => HEADERS: ${options.headers}");
          return handler.next(options);
        },
        onResponse: (response, handler) {
          logger
              .i("RESPONSE[${response.statusCode}] => DATA: ${response.data}");

          return handler.next(response);
        },
        onError: (err, handler) {
          logger.e(
              "Error is[${err.response?.statusCode}] => DATA: ${err.response?.data}");
          switch (err.type) {
            case DioExceptionType.connectionTimeout:
            case DioExceptionType.sendTimeout:
            case DioExceptionType.receiveTimeout:
              throw "Deadline Exceeded Exception";

            case DioExceptionType.cancel:
              break;
            case DioExceptionType.unknown:
              throw "No internet connection";

            default:
          }

          return handler.next(err);
        },
      ),
    );
    return dio!;
  }

  // String? mAccessToken = box.read(CacheKeys.token);
  String? mAccessToken = "";

  DateTime? mAccessExpiresAt;
  var logger = Logger(filter: CustomLogFilter());

  header() async {
    try {
      if (Firebase.apps.isNotEmpty) {
        // Only try to get the token if Firebase is initialized
        mAccessToken = await FirebaseAuth.instance.currentUser?.getIdToken();
      }
      return mAccessToken != null
          ? {
              "Content-Type": "application/json",
              "Authorization": "Bearer $mAccessToken"
            }
          : {"Content-Type": "application/json"};
    } catch (e) {
      logger.e("Error getting Firebase token: $e");
      return {"Content-Type": "application/json"};
    }
  }

  Future<Response> request(
      {required String url,
      required Method method,
      dynamic params,
      Options? options,
      FormData? formdata}) async {
    Response response;

    List<String> urlsToSendLocation = [
      ApiUrls.login,
      ApiUrls.register,
      ApiUrls.updateProfile,
      ApiUrls.create_kitty,
      ApiUrls.transferReq,
      ApiUrls.TRANSFERREQUEST,
      ApiUrls.create_chama,
      ApiUrls.withdrawRequest,
      ApiUrls.withdrawConfirm,
      ApiUrls.contribute_kitty,
      ApiUrls.pay_payment_kitty,
      ApiUrls.chamaContribute,
      ApiUrls.chamaContrConfirm,
      ApiUrls.transferConfirm,
      ApiUrls.TRANSFERCONFIRM,
      ApiUrls.topUp,
      ApiUrls.update_kitty,
      ApiUrls.update_chama,
      ApiUrls.setPin,
      ApiUrls.addSignatory,
      ApiUrls.sigApproval,
      ApiUrls.PROCESSSIGNATORYTRANSACTIONS
    ];

    if (urlsToSendLocation.contains(url)) {
      params ??= {};

      try {
        String? lat = box.read<String>(CacheKeys.lat);
        String? long = box.read<String>(CacheKeys.long);
        String? deviceId = box.read<String>(CacheKeys.deviceId);
        String? imeiNumber = box.read<String>(CacheKeys.imeiNumber);
        String? deviceModel = box.read<String>(CacheKeys.deviceModel);

        if (url.contains(ApiUrls.create_kitty)) {
          params['latitude'] = double.tryParse(lat?.toString() ?? '');
          params['longitude'] = double.tryParse(long?.toString() ?? '');
        } else {
          params['latitude'] = lat;
          params['longitude'] = long;
        }

        // Add device info only if available
        if (deviceId?.isNotEmpty ?? false) params['device_id'] = deviceId;
        if (imeiNumber?.isNotEmpty ?? false) params['imei_code'] = imeiNumber;
        if (deviceModel?.isNotEmpty ?? false) {
          params['device_model'] = deviceModel;
        }
      } catch (e) {
        logger.e('Error adding location/device data: $e');
        // Continue with the request even if location/device data fails
      }
    }

    try {
      if (method == Method.POST) {
        if (formdata == null) {
          response = await dio!.post(url, data: params, options: Options(
            validateStatus: (status) {
              return true;
            },
          ));
        } else {
          response = await dio!.post(url, data: formdata, options: options);
        }
      } else if (method == Method.PUT) {
        response = await dio!.put(url, data: params);
      } else if (method == Method.DELETE) {
        response = await dio!.delete(
          url,
        );
      } else if (method == Method.PATCH) {
        response = await dio!.patch(url);
      } else {
        response = await dio!.get(url);
      }
      return response;
    } on SocketException catch (e) {
      logger.e(e);
      throw Exception("Not Internet Connection");
    } on FormatException catch (e) {
      logger.e(e);

      throw Exception("Bad response format");
    } on DioException catch (e) {
      logger.e(e);
      rethrow;
    } catch (e) {
      logger.e(e);
      throw Exception("Something went wrong");
    }
  }
}
