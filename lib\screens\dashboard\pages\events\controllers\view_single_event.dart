import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';

class ViewSingleEventController extends GetxController implements GetxService {
  Rx<Event> event = Event().obs;
  final transactions = <TransactionModel>[].obs;
  final logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();

  // Track the current event ID to detect changes
  int? _currentEventId;

  @override
  void onInit() {
    super.onInit();
    // Clear any previous state when controller is initialized
    _clearState();
  }

  @override
  void onClose() {
    _clearState();
    super.onClose();
  }

  void _clearState() {
    event.value = Event();
    transactions.clear();
    _currentEventId = null;
  }

  // Method to set event and clear previous state if it's a different event
  void setEvent(Event newEvent) {
    if (_currentEventId != newEvent.id) {
      _clearState();
      _currentEventId = newEvent.id;
    }
    event.value = newEvent;
  }

  Future fetchTransactions(int eventId, {int? size = 20, int? page = 0}) async {
    try {
      // Clear transactions if this is a different event
      if (_currentEventId != eventId) {
        transactions.clear();
        _currentEventId = eventId;
      }

      final response = await apiProvider.request(
        method: Method.GET,
        url:
            "${ApiUrls.EVENTTRANSACTIONS}?event_id=$eventId&size=$size&page=$page",
      );
      if (response.data != null && response.data['data'] != null) {
        final returnedData = response.data['data']['items'] as List;
        transactions.value = returnedData
            .map((item) => TransactionModel.fromJson(item))
            .toList();
      } else {
        Get.snackbar('Error', response.data['message'] ?? 'Failed to load transactions',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      logger.e('Error fetching transactions: $e');
    }
  }
}
