import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:otp_autofill/otp_autofill.dart';
import 'package:pinput/pinput.dart';
class TopUpOtp extends StatefulWidget {
  final String? transId;
  const TopUpOtp({
    super.key,
    this.transId,
  });
 
  @override
  State<TopUpOtp> createState() => _OtpScreen();
}

class _OtpScreen extends State<TopUpOtp> {
  late OTPTextEditController pinController;

  final logger = Get.find<Logger>();

  final box = Get.find<GetStorage>();

  ContributeController contributeController = Get.find();
  UserKittyController userController = Get.find();
  
  late OTPInteractor _otpInteractor;

  @override
  void initState() {
    super.initState();
    _otpInteractor = OTPInteractor();
    _otpInteractor
        .getAppSignature()
        .then((value) => print("signature = $value"));
    pinController = OTPTextEditController(
      codeLength: 6,
      onCodeReceive: (code) {
        if (kDebugMode) {
          print("Your Application receive code = $code");
        }
      },
      otpInteractor: _otpInteractor,
    )..startListenUserConsent((p0) {
        final exp = RegExp(r'(\d{6})');
        return exp.stringMatch(p0 ?? '') ?? '';
      });
  }

  @override
  void dispose() {
    pinController.stopListen();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: TextStyle(
          fontSize: 20,
          color: Theme.of(context).scaffoldBackgroundColor,
          fontWeight: FontWeight.w600),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.5),
        border: Border.all(color: const Color.fromARGB(255, 1, 30, 54)),
        borderRadius: BorderRadius.circular(16),
      ),
    );
    SizeConfig().init(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text("OTP verification"),
        centerTitle: true,
        elevation: 0.3,
        leading: IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
            ),
            onPressed: () => Navigator.of(context).pop()),
      ),
      body: Stack(
        children: [
          Container(
            alignment: Alignment.center,
            child: ListView(
              children: [
                const SizedBox(
                  height: 50,
                ),
                Image.asset(
                  "assets/images/otp.png",
                  semanticLabel: "one kitty otp",
                  height: 50,
                  width: 40,
                ),
                const SizedBox(
                  height: 30,
                ),
                const Center(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text("Enter the OTP code sent to the number provided"),
                  ],
                )),
                const SizedBox(
                  height: 20,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.spMin),
                  child: Obx(
                    () => Column(
                      children: [
                        Pinput(
                          length: 6,
                          controller: pinController,
                          defaultPinTheme: defaultPinTheme,
                          // androidSmsAutofillMethod: AndroidSmsAutofillMethod.smsUserConsent,
                          // listenForMultipleSmsOnAndroid: true,
                          showCursor: true,
                          onCompleted: (pin) async {
                            bool res =
                                await contributeController.contributeProcess(
                                    otp: pinController.text.trim(),
                                    transId: widget.transId ??
                                        userController
                                            .topUpData["checkout_request_id"]);
                            if (!mounted) {
                              return;
                            }
                            if (res) {
                              ToastUtils.showSuccessToast(
                                  context,
                                  contributeController.apiMessageProcess.string,
                                  "Success");

                              Get.back();
                            } else {
                              ToastUtils.showErrorToast(
                                context,
                                contributeController.apiMessageProcess.string,
                                "Error",
                              );
                            }
                          },
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        Visibility(
                          visible: contributeController.isProcessloading.isTrue,
                          child: SpinKitDualRing(
                            color: Theme.of(context).primaryColor,
                            size: 70.0,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 35.spMin,
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                  ),
                  child: Column(
                    children: [
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "Manual Method.",
                            style: TextStyle(
                              decoration: TextDecoration.underline,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        userController.topUpData["customer_message"].toString(),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
