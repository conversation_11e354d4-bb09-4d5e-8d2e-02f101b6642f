// import 'package:android_intent_plus/android_intent.dart';
import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/config.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/connectivity_wrapper.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/screens/dashboard/pages/chama/widgets/create_tabs.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/contribution_kitties/widgets/single_kitty_card.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/test_page.dart';

import 'package:onekitty/screens/dashboard/pages/home/<USER>';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/in_app_browser.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/notification_icon.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/video_player_widget.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/create_kitty/pages/create_kitty.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/main.dart' show isLight;
import 'package:onekitty/screens/dashboard/pages/transactions/models/transaction_type.dart';
import 'package:onekitty/screens/dashboard/pages/transactions/views/screens/transaction_page.dart';
import 'package:onekitty/screens/onboarding/deep_link_helper/deep_link_helper.dart';
import 'package:onekitty/services/init_service.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:upgrader/upgrader.dart';
import '../../../../utils/utils_exports.dart';
import '../profile/profile_page.dart';
import 'widgets/home_dashboard.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool hideBal = false;
  String greeting = getGreeting();
  DataController dataController = Get.put(DataController());
  TextEditingController amountController = TextEditingController();
  ContributeController getKittyController = Get.put(ContributeController());
  Logger logger = Get.find();

  final UserKittyController userController = Get.find<UserKittyController>();
  int activeIndex = 0;
  int activeIndex2 = 0;
  final PageController _pageController =
      PageController(initialPage: 1, viewportFraction: 0.6);
  final PageController _page2Controller = PageController(initialPage: 0);
  final RefreshController _refreshController =
      RefreshController(initialRefresh: true);
// Create an instance of the updater class
  final updater = ShorebirdUpdater();
   Future<void> _checkForUpdates() async {
    // Check whether a new update is available.
    final status = await updater.checkForUpdate();

    if (status == UpdateStatus.outdated) {
      try {
        // Perform the update
        await updater.update();
      } on UpdateException catch (error) {
        // Handle any errors that occur while updating.
        logger.e(error);
      }
    }
  }
  void _onRefresh() async {
    try {
      userController.getLocalUser();
      await userController.getUserkitties();
      await userController.getUser();
      configController.getConfig2();
     /*  updater.readCurrentPatch().then((currentPatch) {
        logger.i('The current patch number is: ${currentPatch?.number}');
      });*/
      await _checkForUpdates(); 
      //verifyVersion();
      _refreshController.refreshCompleted();
    } catch (e) {
      logger.e(e);
      _refreshController.refreshCompleted();
    }
  }



  ConfigController configController = Get.put(ConfigController());
  final containerHeight = 150.0.h.obs;

  @override
  void initState() {
    _onRefresh();
    configController.getConfig2();

    _scrollController.addListener(() {
      final double offset = _scrollController.offset.clamp(0, 160).toDouble();

      containerHeight(150.h - offset.h);
    });

    _initializeApp();
    super.initState();
  }

  // Deeplink Permission request
  final _storage = GetStorage();
  bool _neverShowAgain = false;
  bool _isAndroid12OrHigher = false;
  bool _initialized = false;
  Future<void> _initializeApp() async {
    await _checkAndroidVersion();
    await _checkPermissionStatus();
    setState(() => _initialized = true);
    await getBaseUrl();
  }

  Future<void> _checkAndroidVersion() async {
    final deviceInfo = DeviceInfoPlugin();
    final androidInfo = await deviceInfo.androidInfo;
    setState(() {
      _isAndroid12OrHigher = androidInfo.version.sdkInt >= 31;
    });
  }

  Future<void> _checkPermissionStatus() async {
    if (!_isAndroid12OrHigher) return;

    final shouldShow = _storage.read<bool>('showDeepLinkDialog') ?? true;
    if (shouldShow && mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showPermissionDialog();
      });
    }
  }

  void _showPermissionDialog() {
    // Create a StatefulBuilder to manage state within the modal
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        barrierColor: Colors.black54,
        isDismissible: false,
        builder: (context) => StatefulBuilder(
              builder: (context, setModalState) => Container(
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(24))),
                padding: const EdgeInsets.fromLTRB(24, 32, 24, 24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'link_handling'.tr,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'allow_onekitty_open_links'.tr,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Colors.grey[700],
                          ),
                    ),
                    const SizedBox(height: 24),
                    GestureDetector(
                      onTap: () {
                        // Update both the modal state and parent state
                        setModalState(() => _neverShowAgain = !_neverShowAgain);
                        setState(() {}); // Update parent state
                      },
                      child: Row(
                        children: [
                          Checkbox(
                            value: _neverShowAgain,
                            onChanged: (value) {
                              // Update both the modal state and parent state
                              setModalState(() => _neverShowAgain = value!);
                              setState(() {}); // Update parent state
                            },
                            activeColor: Theme.of(context).primaryColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          Text(
                            'dont_ask_again'.tr,
                            style: TextStyle(
                              color: Colors.grey[700],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                    Divider(color: Colors.grey[200], height: 1),
                    const SizedBox(height: 16),
                    Column(
                      children: [
                        TextButton(
                          onPressed: () => _handleDialogResponse(false),
                          child: Text(
                            'later'.tr,
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () => _handleDialogResponse(true),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColor,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              'allow'.tr,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                                letterSpacing: 0.5,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ));
  }

  Future<void> _handleDialogResponse(bool allow) async {
    Navigator.pop(context);
    _storage.write('showDeepLinkDialog', !_neverShowAgain);
    if (allow) {
      Get.to(() => const DeepLinkHelper());
    }
  }

  animateToMaxMin(double max, double min, double direction, int seconds,
      ScrollController scrollController) {
    if (_pageController.hasClients) {
      scrollController
          .animateTo(direction,
              duration: Duration(seconds: seconds), curve: Curves.linear)
          .then((value) {
        direction = direction == max ? min : max;
        animateToMaxMin(max, min, direction, seconds, scrollController);
      });
    }
  }

  UserModelLatest user = UserModelLatest();
  @override
  void dispose() {
    _pageController.dispose();
    _page2Controller.dispose();

    _scrollController.dispose();
    super.dispose();
  }

  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    // var phoneNumber = Get.find<Eventcontroller>()
    //     .getLocalUser()
    //     ?.phoneNumber
    //     ?.replaceRange(6, 9, "***");
    final screenSize = MediaQuery.of(context).size;
    SizeConfig().init(context);
    return UpgradeAlert(
      showIgnore: false,
      showLater: false,
      dialogStyle: UpgradeDialogStyle.cupertino,
      child: ConnectivityCheck(
        child: Scaffold(
          // appBar: buildAppBarWithImage(context),
          body: SmartRefresher(
            onRefresh: _onRefresh,
            controller: _refreshController,
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              controller: _scrollController,
              slivers: [
                SliverAppBar(
                  leadingWidth: 40.w,
                  actions: <Widget>[
                    if(kDebugMode)...[
                      IconButton(
                          onPressed: () {
                            Get.to(() => const TestPage());
                          },
                          icon: const Icon(Icons.link))
                    ],
                    const NotificationIconWithPulse(),
                  ],
                  pinned: true,
                  expandedHeight: 250.h,
                  // elevation: 1,
                  toolbarHeight: 70.h,
                  backgroundColor: Theme.of(context)
                      .scaffoldBackgroundColor
                      .withOpacity(0.99),
                  leading: AppbarLeadingImage(
                      imagePath:
                          userController.getLocalUser()?.profileUrl != null ||
                                  userController
                                          .getLocalUser()
                                          ?.profileUrl
                                          ?.toString() !=
                                      ""
                              ? userController.getLocalUser()?.profileUrl
                              : null,
                      margin: EdgeInsets.only(left: 5.w, top: 1.h, bottom: 1.h),
                      onTap: () {
                        Navigator.pushAndRemoveUntil(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const ProfilePage()),
                            (route) => route.isActive);
                      }),
                  title: AppbarTitle(
                      textSize: 17,
                      text:
                          "$greeting, ${userController.getLocalUser()!.firstName ?? ''}",
                      margin: EdgeInsets.only(left: 5.w)),
                  bottom: PreferredSize(
                    preferredSize: Size(double.infinity, 100.h),
                    child: Obx(() =>
                        HomeDashboard(containerHeight: containerHeight.value)),
                  ),
                  // flexibleSpace: FlexibleSpaceBar(
                  //   background: Column(
                  //     children: [
                  //       const SizedBox(height: 70),
                  //       ],
                  //   ),
                  // ),
                ),
                SliverToBoxAdapter(
                  child: SizedBox(height: 14.h),
                ),
                SliverToBoxAdapter(
                  child: GetBuilder(
                    init: userController,
                    builder: (controller) {
                      if (userController.kittiesLoading.isTrue) {
                        return SizedBox(
                          height: SizeConfig.screenHeight * 0.1,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SpinKitDualRing(
                                  color: ColorUtil.blueColor,
                                  lineWidth: 4.sp,
                                  size: 40.0.sp,
                                ),
                                Text(
                                  "loading".tr,
                                  style: const TextStyle(
                                    color: Colors.white,
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      } else if (userController.media.isEmpty) {
                        return SizedBox(
                          height: 100,
                          child: Column(
                            children: [
                              Container(
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 12),
                                decoration: BoxDecoration(
                                  color: AppColors.mainPurple,
                                  borderRadius: BorderRadius.circular(16),
                                  image: const DecorationImage(
                                    fit: BoxFit.cover,
                                    image: AssetImage(AssetUrl.img1),
                                  ),
                                ),
                              )
                            ],
                          ),
                        );
                      } else {
                        final medias = userController.media;
                        return Column(
                          // clipBehavior: Clip.none,
                          children: [
                            SizedBox(
                              height: 120.h,
                              child: PageView.builder(
                                  controller: _pageController,
                                  itemCount: medias.length,
                                  onPageChanged: (value) {
                                    setState(() {
                                      activeIndex = value;
                                    });
                                  },
                                  itemBuilder: (context, index) {
                                    // Safety check for index out of bounds
                                    if (index < 0 || index >= medias.length) {
                                      return _buildErrorContainer(
                                          'invalid_media_index'.tr);
                                    }

                                    final media = medias[index];
                                    // Handle potential null media
                                    // if (media == null) {
                                    //   return _buildErrorContainer(
                                    //       'media_not_available'.tr);
                                    // }

                                    try {
                                      if (media.type?.toUpperCase() ==
                                          "VIDEO") {
                                        return CustomVideoPlayer(
                                          url: media.mediaUrl ?? "",
                                          width: 350,
                                          height: 200,
                                        );
                                      } else {
                                        return InkWell(
                                          onTap: () {
                                            if (media.mediaUrl == null ||
                                                media.mediaUrl!.isEmpty) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(SnackBar(
                                                      content: Text(
                                                          "media_not_available".tr)));
                                              return;
                                            }

                                            Get.to(
                                              () => ViewMedia(
                                                media: media,
                                              ),
                                            );
                                          },
                                          child: Container(
                                            margin: const EdgeInsets.symmetric(
                                                horizontal: 12),
                                            decoration: BoxDecoration(
                                              color: AppColors.mainPurple,
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                            ),
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                              child: _buildCachedImage(
                                                  media.mediaUrl),
                                            ),
                                          ),
                                        );
                                      }
                                    } catch (e) {
                                      logger.e(
                                          "Error rendering media at index $index: $e");
                                      return _buildErrorContainer(
                                          'error_loading_media'.tr);
                                    }
                                  }),
                            ),
                            SizedBox(
                              height: 8.h,
                            ),
                            medias.isEmpty
                                ? const SizedBox()
                                : _buildPageIndicators(medias.length)
                          ],
                        );
                      }
                    },
                  ),
                ),
                const SliverToBoxAdapter(
                  child: SizedBox(
                    height: 20,
                  ),
                ),
                SliverToBoxAdapter(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: [
                        SingleLineRow(
                          text: "my_recent_kitty".tr,
                          widget: TextButton(
                              onPressed: () {
                                Get.toNamed(
                                    NavRoutes.myKittiescontribtionScreen);
                              },
                              child: Text("see_all_kitties".tr)),
                        ),
                        SizedBox(
                          height: 170.h,
                          child: GetBuilder(
                              init: userController,
                              initState: (state) {
                                Future.delayed(
                                  const Duration(seconds: 2),
                                  () async {
                                    try {
                                      await state.controller.getUserkitties();
                                    } catch (e) {
                                      Logger().e(e);
                                    }
                                  },
                                );
                              },
                              builder: (UserKittyController userController) {
                                if (userController.kittiesLoading.isTrue) {
                                  return SizedBox(
                                    height: SizeConfig.screenHeight * 0.1,
                                    child: Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          SpinKitDualRing(
                                            color: ColorUtil.blueColor,
                                            lineWidth: 4.sp,
                                            size: 40.0.sp,
                                          ),
                                          Text(
                                            "loading".tr,
                                            style: const TextStyle(
                                              color: Colors.white,
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  );
                                } else if (userController.kitties.isEmpty) {
                                  return SizedBox(
                                    //height: 60,
                                    child: Column(
                                      children: [
                                        //Text("You dont have any kitties", style: context.titleLarge,),
                                        Image.asset(
                                          AssetUrl.notFound,
                                          height: 130.h,
                                        ),
                                        CustomKtButton(
                                          height: 40.h,
                                          btnText: "create_a_kitty".tr,
                                          onPress: () {
                                            showGeneralDialog(
                                              context: context,
                                              barrierDismissible: true,
                                              barrierLabel: 'close'.tr,
                                              barrierColor:
                                                  Colors.black.withOpacity(0.5),
                                              transitionDuration:
                                                  const Duration(
                                                      milliseconds: 200),
                                              pageBuilder:
                                                  (BuildContext context,
                                                      Animation animation,
                                                      Animation
                                                          secondaryAnimation) {
                                                return const CreateTabs();
                                              },
                                              transitionBuilder:
                                                  (BuildContext context,
                                                      Animation<double>
                                                          animation,
                                                      Animation<double>
                                                          secondaryAnimation,
                                                      Widget child) {
                                                return ScaleTransition(
                                                  scale: animation,
                                                  child: child,
                                                );
                                              },
                                            );
                                          },
                                        )
                                      ],
                                    ),
                                  );
                                } else if (userController.kitties.isNotEmpty) {
                                  return SizedBox(
                                    height: 170.h,
                                    child: PageView.builder(
                                      scrollDirection: Axis.horizontal,
                                      controller: _page2Controller,
                                      onPageChanged: (value) {
                                        setState(() {
                                          activeIndex2 = value;
                                        });
                                        print(
                                            "Kitties index is: $activeIndex2");
                                      },
                                      itemCount:
                                          userController.kitties.length < 5
                                              ? userController.kitties.length
                                              : userController.kitties
                                                  .sublist(0, 5)
                                                  .length,
                                      itemBuilder: (context, index) {
                                        final kitty =
                                            userController.kitties[index];
                                        return Padding(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 6.0.h, horizontal: 8.w),
                                          child: ContributionKittyWidget(
                                            kitty: kitty,
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                }
                                return SizedBox(
                                  child: Column(
                                    children: [
                                      Text("you_dont_have_any_kitties".tr),
                                      Image.asset(
                                        AssetUrl.notFound,
                                        height: 150.h,
                                      ),
                                      CustomKtButton(
                                        height: 50,
                                        btnText: "create_a_kitty".tr,
                                        onPress: () {
                                          Get.to(() => const StepOne());
                                        },
                                      )
                                    ],
                                  ),
                                );
                              }),
                        ),
                        SizedBox(
                          height: 10.h,
                        ),
                        userController.kitties.isEmpty
                            ? const SizedBox()
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: List<Widget>.generate(
                                    userController.kitties.length < 5
                                        ? userController.kitties.length
                                        : userController.kitties
                                            .sublist(0, 5)
                                            .length,
                                    //userController.kitties.length,
                                    (index) => Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 3.0),
                                          child: InkWell(
                                            onTap: () {
                                              _page2Controller.animateToPage(
                                                  index,
                                                  duration: const Duration(
                                                      milliseconds: 600),
                                                  curve: Curves.easeIn);
                                            },
                                            child: CircleAvatar(
                                              radius: 5,
                                              backgroundColor:
                                                  activeIndex2 == index
                                                      ? AppColors.primary
                                                      : AppColors.neutral,
                                            ),
                                          ),
                                        )),
                              ),
                        SizedBox(
                          height: 8.h,
                        ),
                      ],
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: DefaultTabController(
                    length: 2,
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.all(5.h),
                          margin: const EdgeInsets.symmetric(horizontal: 20),
                          decoration: AppDecoration.fillSlate.copyWith(
                            borderRadius: BorderRadiusStyle.roundedBorder6,
                          ),
                          child: TabBar(
                            physics: const ClampingScrollPhysics(),
                            //padding: const EdgeInsets.only(left: 5, right: 5),
                            unselectedLabelColor:
                                isLight.value ? Colors.black : Colors.white70,
                            labelColor: Theme.of(context).primaryColor,
                            indicatorSize: TabBarIndicatorSize.tab,
                            dividerColor: Colors.transparent,
                            indicator: BoxDecoration(
                                borderRadius: BorderRadius.circular(5),
                                color: isLight.value
                                    ? Colors.white
                                    : Colors.black45),
                            tabs: [
                              Tab(
                                child: Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 1.w),
                                  child: Text("services".tr),
                                ),
                              ),
                              Tab(
                                child: Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 1.w),
                                  child: Text("my_transactions".tr),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: screenSize.height * 0.7.h,
                          child: TabBarView(
                            children: [HomeServices(), TransactionPage(
                              config: TransactionPageConfig(
                                transactionType: TransactionType.user,
                                entityId: dataController.kitty.value.kitty?.iD  ?? 0,
                                title: 'my_transactions'.tr,
                                isFullPage: false,
                                showExportOptions: true,
                                showEditOptions: false,
                              ),
                            ),],
                          ),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorContainer(String message) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: Text(
          message,
          style: TextStyle(color: Colors.grey[700]),
        ),
      ),
    );
  }

  Widget _buildCachedImage(String? url) {
    if (url == null || url.isEmpty) {
      return Image.asset(
        AssetUrl.launcher,
        fit: BoxFit.cover,
      );
    }

    return FastCachedImage(
      url: url,
      fit: BoxFit.cover,
      fadeInDuration: const Duration(milliseconds: 300),
      errorBuilder: (context, error, stackTrace) {
        logger.e("Error loading image: $error");
        return Stack(
          fit: StackFit.expand,
          children: [
            Image.asset(
              AssetUrl.launcher,
              fit: BoxFit.cover,
            ),
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.error_outline,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ],
        );
      },
      loadingBuilder: (context, progress) {
        return Container(
          color: isLight.value ? Colors.white : Colors.black,
          child: Center(
            child: Image.asset(
              AssetUrl.launcher,
              width: 80,
              height: 80,
            ),
          ),
        );
      },
    );
  }

  Widget _buildPageIndicators(int count) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List<Widget>.generate(
          count,
          (index) => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 3.0),
                child: InkWell(
                  onTap: () {
                    _pageController.animateToPage(index,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeIn);
                  },
                  child: CircleAvatar(
                    radius: 5,
                    backgroundColor: activeIndex == index
                        ? AppColors.primary
                        : AppColors.neutral,
                  ),
                ),
              )),
    );
  }
}

buildPages(String img) {
  return Image.asset(
    img,
    fit: BoxFit.cover,
  );
}
