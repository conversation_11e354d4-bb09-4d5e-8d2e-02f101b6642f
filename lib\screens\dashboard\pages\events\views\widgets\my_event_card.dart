import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/view_single_event_organizer.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/attendees_widget.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/my_quill_editor.dart' show QuillEditorShortWidget;
import 'package:onekitty/utils/show_cached_network_image.dart';
import 'package:onekitty/utils/utils_exports.dart';
import '../../../../../../utils/event_details.dart';
import 'package:get/get.dart';
import '../../../../../../main.dart' as main show isLight;
import 'package:onekitty/screens/dashboard/pages/events/controllers/view_single_event.dart';
import '../../../../../../utils/event_description_optimizer.dart';

// Custom page route for smoother hero animations
class HeroPageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  
  HeroPageRoute({required this.page})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 600),
          reverseTransitionDuration: const Duration(milliseconds: 500),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // Fade animation
            var fadeAnimation = CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
            );
            
            // Scale animation
            var scaleAnimation = Tween<double>(
              begin: 0.95,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            ));
            
            return FadeTransition(
              opacity: fadeAnimation,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: child,
              ),
            );
          },
        );
}

class MyEventCards extends StatefulWidget {
  final bool? organizer;
  final MyEventsModel eventModel;
  const MyEventCards({super.key, required this.eventModel, this.organizer});

  @override
  State<MyEventCards> createState() => _MyEventCardsState();
}

class _MyEventCardsState extends State<MyEventCards> with AutomaticKeepAliveClientMixin {
  // Cache computed values to avoid recalculating on every build
  late final String _imageUrl;
  late final String _locationText;
  late final String _dateTimeText;
  late final String _formattedBalance;
  late final String _uniqueSuffix;
  late final String _heroImageTag;
  late final String _heroTextTag;
  late final String _heroCollectedTag;
  late final String _locationId;
  late final String _dateId;
  late final String _quillTag;

  @override
  bool get wantKeepAlive => true; // Keep widget alive to maintain state

  @override
  void initState() {
    super.initState();
    _precomputeValues();
  }

  void _precomputeValues() {
    final event = widget.eventModel.event;
    final eventId = event.id ?? 0;

    // Generate unique suffix once and cache it
    _uniqueSuffix = '${eventId}_${widget.eventModel.hashCode}';

    // Precompute image URL
    _imageUrl = (event.eventMedia?.isNotEmpty == true && event.eventMedia![0].url?.isNotEmpty == true)
        ? event.eventMedia![0].url!
        : AssetUrl.onekittyBannnerUrl;

    // Precompute location text
    _locationText = "${event.locationTip} - ${event.venue}";

    // Precompute date/time text (only if startDate is not null)
    if (event.startDate != null) {
      final localDate = event.startDate!.toLocal();
      _dateTimeText = '${formatDate(localDate.toString())}, ${formatTime(localDate.toString())}';
    } else {
      _dateTimeText = 'Date TBD';
    }

    // Precompute formatted balance
    _formattedBalance = FormattedCurrency.getFormattedCurrency(event.balance ?? 0.0);

    // Precompute hero tags and IDs
    _heroImageTag = 'image:${eventId}o:card:$_uniqueSuffix';
    _heroTextTag = 'text:${eventId}o:$_uniqueSuffix';
    _heroCollectedTag = 'collected${eventId}o:$_uniqueSuffix';
    _locationId = "location${eventId}o:$_uniqueSuffix";
    _dateId = "date${eventId}o:$_uniqueSuffix";
    _quillTag = EventDescriptionOptimizer.generateUniqueTag(eventId, _uniqueSuffix);
  }

  void _navigateToEvent() {
    // Clear any cached descriptions to prevent showing previous event data
    EventDescriptionOptimizer.clearAllCaches();

    // Update the event model with the hero tag suffix before navigating
    final updatedEventModel = MyEventsModel(
      event: widget.eventModel.event,
      count: widget.eventModel.count,
      hasSignatoryTransactions: widget.eventModel.hasSignatoryTransactions,
      heroTagSuffix: _uniqueSuffix,
    );

    // Properly set the event in the controller to clear previous state
    final controller = Get.put(ViewSingleEventController());
    controller.setEvent(widget.eventModel.event);

    // Use custom page route for smoother hero animations
    Navigator.push(
      context,
      HeroPageRoute(
        page: ViewSingleEventOrganizer(
          eventmodel: updatedEventModel,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return GestureDetector(
      onTap: _navigateToEvent,
      child: ValueListenableBuilder<bool>(
        valueListenable: main.isLight,
        builder: (context, isLight, _) {
          return Card(
            elevation: 4,
            margin: const EdgeInsets.all(8),
            color: isLight ? Colors.white : const Color(0xff26262e),
            child: Padding(
              padding: EdgeInsets.only(
                left: 6.spMin,
                right: 6.spMin,
                top: 6.spMin,
                bottom: 10.spMin,
              ),
              child: Column(
                children: [
                  Hero(
                    tag: _heroImageTag,
                    child: AdaptiveCachedNetworkImage(
                      imageUrl: _imageUrl,
                      initialWidth: 390.w,
                      borderRadius: 8.r,
                      initialHeight: 300.h,
                    ),
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Material(
                          color: Colors.transparent,
                          textStyle: TextStyle(
                            color: Colors.grey.shade700,
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 8.0),
                                child: Hero(
                                  tag: _heroTextTag,
                                  child: Material(
                                    color: Colors.transparent,
                                    child: Text(
                                      widget.eventModel.event.title,
                                      style: TextStyle(
                                        color: isLight ? Colors.black : Colors.white,
                                        fontWeight: FontWeight.w700,
                                        fontSize: 20,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              EventDetailsWidget(
                                id: _locationId,
                                label: _locationText,
                                image: 'assets/images/icons/location.png',
                                icon: Icons.location_on_outlined,
                              ),
                              EventDetailsWidget(
                                id: _dateId,
                                image: 'assets/images/icons/calendar.png',
                                label: _dateTimeText,
                                icon: Icons.calendar_month,
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (widget.organizer ?? false)
                        Hero(
                          tag: _heroCollectedTag,
                          child: Material(
                            color: Colors.transparent,
                            child: Text.rich(
                              textAlign: TextAlign.end,
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: "$_formattedBalance\n",
                                    style: const TextStyle(
                                      color: Color(0xff4355b6),
                                      fontSize: 30,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const TextSpan(text: 'Collected'),
                                ],
                              ),
                            ),
                          ),
                        )
                    ],
                  ),
                  Align(
                    alignment: Alignment.topLeft,
                    child: QuillEditorShortWidget(
                      maxLines: 2,
                      text: widget.eventModel.event.description,
                      tag: _quillTag,
                    ),
                  ),
                  if (widget.organizer ?? false)
                    AttendeesWidget(
                      count: widget.eventModel.count,
                      size: 18,
                      textSize: 15,
                    )
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
