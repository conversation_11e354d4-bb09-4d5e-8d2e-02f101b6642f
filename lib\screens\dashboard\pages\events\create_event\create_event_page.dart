// import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/controllers.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/events_controller.dart'; 
import 'package:onekitty/screens/dashboard/pages/events/create_event/event_completed_page.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/event_details.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/socials.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/tickets.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/time_and_location.dart';
import 'package:onekitty/utils/iswysiwyg.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/show_snackbar.dart';
import 'package:onekitty/utils/stepper_indicator.dart';
import '../controllers/create_event_controller.dart';
import '/utils/date_formatter.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:onekitty/main.dart' show isLight;

class CreateEventPage extends StatefulWidget {
  const CreateEventPage({super.key});

  @override
  State<CreateEventPage> createState() => CreateEventPageState();
}

class CreateEventPageState extends State<CreateEventPage> {
  PageController pageController = PageController();
  late ValueNotifier<int> currentPage = ValueNotifier(0);
  final q.QuillController eventDescription = q.QuillController.basic();
  final TextEditingController eventTitle = TextEditingController(),
      emailAddress = TextEditingController(),
      phoneNumber = TextEditingController(
          text: Get.find<Eventcontroller>().getLocalUser()?.phoneNumber ?? "");
  final GlobalKey<FormState> _formKey1 = GlobalKey<FormState>();
  // final ValueNotifier<String?> countryCode = ValueNotifier(null);
//controllers for page 2
  final TextEditingController eventStartDate = TextEditingController(),
      eventEndDate = TextEditingController(),
      venue = TextEditingController(),
      referralCode =
          TextEditingController(text: Get.find<GlobalControllers>().getCode()),
      location = TextEditingController();
  final GlobalKey<FormState> _formKey2 = GlobalKey<FormState>();
  //controllers for page 4
  final TextEditingController website = TextEditingController(),
      facebook = TextEditingController(),
      xlink = TextEditingController(),
      instagram = TextEditingController(),
      tiktok = TextEditingController();
  final GlobalKey<FormState> _formKey3 = GlobalKey<FormState>();
  final controller = Get.find<CreateEventController>();

  @override
  void dispose() {
    eventTitle.dispose();
    eventDescription.dispose();
    emailAddress.dispose();
    phoneNumber.dispose();
    eventStartDate.dispose();
    eventEndDate.dispose();
    venue.dispose();
    location.dispose();
    website.dispose();
    facebook.dispose();
    xlink.dispose();
    instagram.dispose();
    tiktok.dispose();
    pageController.dispose();
    currentPage.dispose();
    super.dispose();
  }

  void _handleBack() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('exit_event_creation'.tr),
        content: Text('progress_will_be_lost'.tr),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('cancel'.tr),
          ),
          TextButton(
            onPressed: () {
              // Clean up controller variables when user exits
              controller.cleanupController();
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Exit page
            },
            child: Text('exit'.tr),
          ),
        ],
      ),
    );
  }

  /// Validates social media URLs
  String? _validateSocialMediaUrls() {
    final Map<String, String> urlFields = {
      'Website': website.text.trim(),
      'Facebook': facebook.text.trim(),
      'X (Twitter)': xlink.text.trim(),
      'Instagram': instagram.text.trim(),
      'TikTok': tiktok.text.trim(),
    };

    for (final entry in urlFields.entries) {
      final fieldName = entry.key;
      final url = entry.value;
      
      if (url.isNotEmpty) {
        // Check if URL is valid
        if (!_isValidUrl(url)) {
          return 'website_url_not_valid'.tr.replaceAll("{field}", fieldName);
        }
        
        // Check platform-specific URL patterns
        if (!_isValidPlatformUrl(fieldName, url)) {
          return 'url_not_valid_platform'.tr.replaceAll("{field}", fieldName).replaceAll( '{platform}', fieldName.toLowerCase());
        }
      }
    }
    
    return null; // All URLs are valid
  }

  /// Checks if a URL is valid
  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https') && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  /// Validates platform-specific URL patterns
  bool _isValidPlatformUrl(String platform, String url) {
    final lowerUrl = url.toLowerCase();
    
    switch (platform.toLowerCase()) {
      case 'facebook':
        return lowerUrl.contains('facebook.com') || lowerUrl.contains('fb.com');
      case 'x (twitter)':
        return lowerUrl.contains('twitter.com') || lowerUrl.contains('x.com');
      case 'instagram':
        return lowerUrl.contains('instagram.com');
      case 'tiktok':
        return lowerUrl.contains('tiktok.com');
      case 'website':
        return true; // Any valid URL is acceptable for website
      default:
        return true;
    }
  }

  /// Validates media URLs for completeness and validity
  String? _validateMediaUrls() {
    for (int i = 0; i < controller.eventMedia.length; i++) {
      final media = controller.eventMedia[i];
      final url = media['url']?.toString();
      
      if (url == null || url.isEmpty) {
        return 'media_upload_incomplete'.tr.replaceAll("{index}", (i + 1).toString());
      }
      
      if (!_isValidUrl(url)) {
        return 'media_invalid_url'.tr.replaceAll("{index}", (i + 1).toString());
      }
    }
    return null;
  }

  void validator(int pageNo) async {
    try {
      if (pageNo == 0) {
        if (_formKey1.currentState!.validate()) {
          // Optional media: validate any uploaded media URLs if present
          String? mediaValidationError = _validateMediaUrls();
          if (mediaValidationError != null) {
            showSnackbar(
                context: context, 
                label: mediaValidationError);
            return;
          }
          
          pageController.animateToPage(currentPage.value + 1,
              duration: const Duration(microseconds: 500),
              curve: Curves.linear);
        }
      } else if (pageNo == 1) {
        if (_formKey2.currentState!.validate()) {
          try {
            final DateTime startDate =
                DateFormat('dd/MM/yyyy hh:mm a').parse(eventStartDate.text);
            final DateTime endDate =
                DateFormat('dd/MM/yyyy hh:mm a').parse(eventEndDate.text);

            if (endDate.isBefore(startDate)) {
              showSnackbar(
                  context: context, label: 'end_date_after_start_date'.tr);
              return;
            }

            if (endDate.difference(startDate).inMinutes < 30) {
              showSnackbar(
                  context: context,
                  label: 'event_minimum_30_minutes'.tr);
              return;
            }

            // Validate location data
            final locationController = Get.find<TimeAndLocationController>();
            if (locationController.mapCoordinates['lat'] == null ||
                locationController.mapCoordinates['long'] == null) {
              showSnackbar(
                  context: context,
                  label: 'please_select_location_map'.tr);
              return;
            }

            // Just validate and move to next page - don't create event yet
            pageController.animateToPage(currentPage.value + 1,
                duration: const Duration(microseconds: 500),
                curve: Curves.linear);
          } catch (e) {
            showSnackbar(context: context, label: 'invalid_date_format'.tr);
            return;
          }
        }
      } else if (pageNo == 2) {
        if (controller.tickets.isNotEmpty) {
          try {
            final DateTime startDate = parseCustomDateTime(eventStartDate.text);
            final DateTime endDate = parseCustomDateTime(eventEndDate.text);

            // Sync and validate media before creating event
            controller.syncMediaLists();
            final validatedMedia = controller.getValidatedEventMedia();
            
            final Eventid = await controller
                .createEventWithTickets(
                    eventMedia: validatedMedia,
                    title: eventTitle.text,
                    username:
                        "${eventTitle.text.toLowerCase().replaceAll(RegExp(r'\s+'), '-')}${DateTime.now().millisecondsSinceEpoch}",
                    description: quilltoHtml(eventDescription),
                    phoneNumber: phoneNumber.text,
                    email: emailAddress.text,
                    locationTip: location.text,
                    venue: venue.text,
                    
                    referralCode: referralCode.text.isEmpty
                        ? null
                        : int.tryParse(referralCode.text),
                    lat: Get.find<TimeAndLocationController>()
                            .mapCoordinates['lat'] ??
                        0.0,
                    long: Get.find<TimeAndLocationController>()
                            .mapCoordinates['long'] ??
                        0.0,
                    catId: controller.category.value,
                    startDate: startDate,
                    endDate: endDate,
                    tickets: controller.tickets
                )
                .onError((e, s) {
              showSnackbar(context: context, label: '${'error_creating_event'.tr}: $e');
              return 0;
            });
            
            
              if (Eventid == 0) {
                Get.snackbar('error'.tr, 'could_not_create_event'.tr);
              } else {
                Get.find<GlobalControllers>().clearCode();
                pageController.animateToPage(currentPage.value + 1,
                    duration: const Duration(microseconds: 500),
                    curve: Curves.linear);
              }
          
          } catch (e) {
            showSnackbar(context: context, label: '${'error_creating_event'.tr}: $e');
          }
        } else {
          showSnackbar(context: context, label: 'add_atleast_one_ticket'.tr);
        }
      } else if (pageNo == 3) {
        try {
          // Validate social media URLs before submission
          String? validationError = _validateSocialMediaUrls();
          if (validationError != null) {
            showSnackbar(context: context, label: validationError);
            return;
          }
          
          await controller
              .addSocialMedia(
                  eventId: controller.eventId.value,
                  media: [],
                  facebook: facebook.text.trim().isEmpty ? null : facebook.text.trim(),
                  tiktok: tiktok.text.trim().isEmpty ? null : tiktok.text.trim(),
                  instagram: instagram.text.trim().isEmpty ? null : instagram.text.trim(),
                  youtube: "",
                  twitter: xlink.text.trim().isEmpty ? null : xlink.text.trim(),
                  hearthis: "",
                  website: website.text.trim().isEmpty ? null : website.text.trim())
              .whenComplete(() {
            final _eventsController = Get.put(Eventcontroller());
            _eventsController.refreshEvents();
            _eventsController.refreshUserEvents();
            pageController.animateToPage(currentPage.value + 1,
                duration: const Duration(microseconds: 500),
                curve: Curves.linear);
          });
        } catch (e) {
          showSnackbar(
              context: context, label: '${'error_adding_social_media'.tr}: $e');
        }
      }
    } catch (e) {
      showSnackbar(context: context, label: '${'an_error_occurred'.tr}: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    CreateEventController controller = Get.put(CreateEventController());
    return WillPopScope(
        onWillPop: () async {
          _handleBack();
          return false;
        },
        child: Scaffold(
            appBar: AppBar(
              leadingWidth: 100.w,
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
              elevation: 4,
              title: Text(
                'create_an_event'.tr,
                style: TextStyle(
                  color: isLight.value ? Colors.black : Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 20.sp,
                ),
              ),
              leading: TextButton.icon(
                icon: Icon(Icons.arrow_back,
                    color: isLight.value ? Colors.black : null),
                onPressed: _handleBack,
                label: Text('back'.tr),
              ),
            ),
            floatingActionButton: ValueListenableBuilder(
                valueListenable: currentPage,
                builder: (context, _currentPage, child) {
                  if (_currentPage == 0) {
                    return Material(
                      borderRadius: BorderRadius.circular(25),
                      color: primaryColor,
                      child: MaterialButton(
                          onPressed: () {
                            validator(_currentPage);
                          },
                          child:   Text(
                            'next'.tr,
                            style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 16),
                          )),
                    );
                  }
                  if (_currentPage > 3) {
                    return const SizedBox();
                  }
                  return Padding(
                    padding: EdgeInsets.only(left: 18.0.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        _currentPage == 2
                            ? const SizedBox()
                            : TextButton(
                                onPressed: () {
                                  pageController.jumpToPage(
                                    currentPage.value - 1,
                                  );
                                },
                                child: Text('back'.tr)),
                        Obx(
                          () => Visibility(
                            visible: !(_currentPage == 2 &&
                                controller.tickets.isEmpty),
                            child: MyButton(
                              onClick: () async {
                                validator(_currentPage);
                              },
                              label: _currentPage == 3 ? 'finish'.tr : 'next'.tr,
                              showLoading: controller.isloading.value,
                            ),
                          ),
                        )
                      ],
                    ),
                  );
                }),
            body: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  children: [
                    ValueListenableBuilder(
                        valueListenable: currentPage,
                        builder: (context, _currentPage, child) {
                          if (_currentPage > 3) {
                            return const SizedBox();
                          }
                          return Column(
                            children: [
                              StepperIndicator(currentStep: _currentPage),
                            ],
                          );
                        }),
                    SizedBox(height: 20.h),
                    Expanded(
                      child: PageView(
                        physics: const NeverScrollableScrollPhysics(),
                        reverse: false,
                        controller: pageController,
                        onPageChanged: (page) => currentPage.value = page,
                        children: [
                          EventDetails(
                              formKey: _formKey1,
                              eventTitle: eventTitle,
                              eventDescription: eventDescription,
                              emailAddress: emailAddress,
                              phoneNumber: phoneNumber),
                          TimeAndLocation(
                              referralCode: referralCode,
                              formKey: _formKey2,
                              eventStartDate: eventStartDate,
                              eventEndDate: eventEndDate,
                              venue: venue,
                              location: location),
                          const Tickets(),
                          Socials(
                              website: website,
                              facebook: facebook,
                              xlink: xlink,
                              instagram: instagram,
                              tiktok: tiktok),
                          EventCompletedPage(
                            username: '${controller.eventUsername} ',
                            eventName: '${eventTitle.text} ',
                          ),
                        ],
                      ),
                    ),
                  ],
                ))));
  }
}

class TicketUpload {
  final String ticketTitle;
  final int? ticketQuantity;
  final int ticketPrice;
  final String ticketType;
  final int? ticketGroupSize;
  final String ticketDescription;
  final String ticketStartDate;
  final String ticketEndDate;
  const TicketUpload({
    required this.ticketTitle,
    this.ticketQuantity,
    required this.ticketPrice,
    required this.ticketType,
    this.ticketGroupSize,
    required this.ticketDescription,
    required this.ticketStartDate,
    required this.ticketEndDate,
  });
}
