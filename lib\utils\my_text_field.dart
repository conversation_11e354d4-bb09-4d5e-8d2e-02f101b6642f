import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/controllers.dart';
import 'package:onekitty/main.dart' show isLight;
import 'package:onekitty/utils/custom_text_style.dart';

class MyTextFieldwValidator extends StatelessWidget {
  final String? title, hint;
  final TextEditingController? controller;
  final TextStyle? titleStyle;
  final Widget? iconSuffix, iconPreffix;
  final TextInputType? keyboardType;
  final bool? readOnly;
  final int? maxLength;
  final Function()? onTap;
  final Function(String val)? onChanged;
  final String? Function(String?)? validator;
  final bool allowAlphanumeric;
  final List<TextInputFormatter>? inputFormatters;

  const MyTextFieldwValidator({
    super.key,
    this.title,
    this.hint,
    this.controller,
    this.titleStyle,
    this.iconSuffix,
    this.keyboardType,
    this.validator,
    this.readOnly,
    this.onTap,
    this.iconPreffix,
    this.maxLength,
    this.onChanged,
    this.allowAlphanumeric = false,
    this.inputFormatters,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null)
          Material(
            color: Colors.transparent,
            textStyle: TextStyle(
              fontSize: 14,
              color: isLight.value ? Colors.black : Colors.white,
              fontWeight: FontWeight.w600,
            ),
            child: Text(
              "$title",
              style: titleStyle?.copyWith(
                color: isLight.value ? null : Colors.white,
              ),
            ),
          ),
        if (title != null) SizedBox(height: 8.h),
        SizedBox(
          width: 360.w,
          child: TextFormField(
            controller: controller,
            onChanged: onChanged,
            onTap: onTap,
            keyboardType: keyboardType,
            validator: validator,
            readOnly: readOnly ?? false,
            maxLength: maxLength,
            inputFormatters: inputFormatters ?? (allowAlphanumeric 
                ? [FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]'))]
                : null),
            decoration: InputDecoration(
              suffixIcon: iconSuffix,
              prefixIcon: iconPreffix,
              hintText: hint,
              hintStyle: CustomTextStyles.hintTextStyle,
              filled: true,
              // fillColor:
              //     isLight.value ? Colors.white70 : Colors.transparent,
          /*    border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  width: 0.5,
                  color: isLight.value ? Colors.grey : Colors.grey[600]!,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  width: 0.5,
                  color: isLight.value ? Colors.grey : Colors.grey[600]!,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  width: 1,
                  color: Theme.of(context).primaryColor,
                ),
              ),
          */
            ),
          ),
        ),
      ],
    );
  }
}

class MyDropdownMenu extends StatelessWidget {
  final TextEditingController? controller;
  final List<String> lists;
  final Function(String? val)? onSelected;
  final String? title;
  final TextStyle? titleStyle;
  final double? width;

  const MyDropdownMenu(
      {super.key,
      this.controller,
      required this.lists,
      this.onSelected,
      this.title,
      this.titleStyle,
      this.width});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null)
          Material(
            color: Colors.transparent,
            textStyle: TextStyle(
              fontSize: 14.spMin,
              color: isLight.value ? Colors.black : Colors.white,
              fontWeight: FontWeight.w600,
            ),
            child: Text(
              "$title",
              // style: titleStyle,
            ),
          ),
        if (title != null) const SizedBox(height: 8),
        DropdownMenu(
          onSelected: onSelected,
          width: width ?? 360.w,
          inputDecorationTheme: InputDecorationTheme(
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.grey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade400),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: primaryColor),
            ),
          ),
          controller: controller,
          dropdownMenuEntries: lists.map((type) {
            return DropdownMenuEntry(value: type, label: type);
          }).toList(),
        ),
      ],
    );
  }
}

class MyTextField extends StatelessWidget {
  final String? title, hint;
  final TextEditingController? controller;
  final TextStyle? titleStyle;
  final Widget? iconSuffix;
  final String? errorText;
  final TextInputType? keyboardType;

  const MyTextField({
    super.key,
    this.title,
    this.hint,
    this.controller,
    this.titleStyle,
    this.iconSuffix,
    this.errorText,
    this.keyboardType,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null)
          Material(
            color: Colors.transparent,
            textStyle: TextStyle(
                fontSize: 14.spMin,
                color: !isLight.value ? Colors.white70 : Colors.black,
                fontWeight: FontWeight.w600),
            child: Text(
              "$title",
              // style: titleStyle,
            ),
          ),
        if (title != null) SizedBox(height: 8.h),
        SizedBox(
          width: 360.w,
          child: TextField(
            controller: controller,
            keyboardType: keyboardType,
            decoration: InputDecoration(
              suffixIcon: iconSuffix,
              hintText: hint,
              hintStyle: CustomTextStyles.hintTextStyle,
              filled: true,
              fillColor:  isLight.value ? Colors.white70 : Colors.transparent,
              errorText: errorText,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: const BorderSide(
                  width: 0.5,
                  color: Colors.grey,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: const BorderSide(
                  width: 0.5,
                  color: Colors.grey,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
                borderSide: const BorderSide(
                  width: 1,
                  color: primaryColor,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
