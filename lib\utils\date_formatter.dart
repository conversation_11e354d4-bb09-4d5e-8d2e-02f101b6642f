import 'package:intl/intl.dart';

// Cache DateFormat instances to avoid recreating them
class _DateFormatters {
  static final DateFormat _dateFormatter = DateFormat('dd MMM yyyy');
  static final DateFormat _timeFormatter = DateFormat('h:mm a');
  static final DateFormat _dateTimeFormatter = DateFormat("dd/MM/yyyy h:mm a");

  static DateFormat get date => _dateFormatter;
  static DateFormat get time => _timeFormatter;
  static DateFormat get dateTime => _dateTimeFormatter;
}

// Cache for parsed dates to avoid reparsing the same strings
final Map<String, DateTime> _dateCache = <String, DateTime>{};
const int _maxCacheSize = 100; // Limit cache size to prevent memory leaks

DateTime _parseDateTime(String dateString) {
  // Check cache first
  if (_dateCache.containsKey(dateString)) {
    return _dateCache[dateString]!;
  }

  DateTime parsedDate;
  try {
    parsedDate = DateTime.parse(dateString != 'null' ? dateString : '2024-01-04T22:57:57.477Z');
  } catch (e) {
    parsedDate = DateTime.parse("2024-01-04T22:57:57.477Z");
  }

  // Add to cache, but limit cache size
  if (_dateCache.length >= _maxCacheSize) {
    // Remove oldest entry (simple FIFO)
    final firstKey = _dateCache.keys.first;
    _dateCache.remove(firstKey);
  }
  _dateCache[dateString] = parsedDate;

  return parsedDate;
}

String formatDate(String dateString) {
  final parsedDate = _parseDateTime(dateString);
  return _DateFormatters.date.format(parsedDate);
}

String formatTime(String dateString) {
  final parsedDate = _parseDateTime(dateString);
  return _DateFormatters.time.format(parsedDate);
}

//This function returns DateTime variable from the above function's output
DateTime parseDateTime(String date, String time) {
  // Combine the date and time strings
  String dateTime = "$date $time";

  // Use cached formatter
  DateTime parsedDateTime = _DateFormatters.dateTime.parse(dateTime);

  return parsedDateTime;
}

String parseDateTimeToIso(String date, String time) {
  // Combine the date and time strings
  String dateTime = "$date $time";

  try {
    // Parse the combined date and time string into a DateTime object using cached formatter
    DateTime parsedDateTime = _DateFormatters.dateTime.parse(dateTime);

    // Convert to ISO 8601 string with UTC timezone (Z at the end)
    return parsedDateTime.toUtc().toIso8601String();
  } catch (e) {
    return ''; // Return an empty string or handle the error as appropriate
  }
}

String convertToIso8601(String dateTimeString) {
  // Preprocess the string to remove extra spaces around the colon in the time
  String cleanedDateTimeString = dateTimeString.replaceAll(' : ', ':');

  // Define the input format
  DateFormat inputFormat = DateFormat('dd/MM/yyyy hh:mm a');

  // Parse the cleaned string into DateTime
  DateTime parsedDate = inputFormat.parse(cleanedDateTimeString.trim());

  // Convert to ISO 8601 string
  return parsedDate.toLocal().toUtc().toIso8601String();
}

String parseDateTimeToIso2(String datetime) {
  // Define the format pattern that matches the input
  DateFormat format = DateFormat("dd/MM/yyyy HH:mm");

  try {
    // Parse the date and time string into a DateTime object
    DateTime parsedDateTime = format.parse(datetime);

    // Convert to ISO 8601 string with UTC timezone (Z at the end)
    return parsedDateTime.toLocal().toUtc().toIso8601String();
  } catch (e) {
    return ''; // or handle the error as appropriate for your use case
  }
}
String convertSingleStringToISO8601(String dateTime) {
  try {
    dateTime = dateTime.trim();
    DateTime parsedDateTime;

    if (dateTime.contains('T')) {
      // Format: YYYY-MM-DDTHH:MM:SS.SSSSSS
      parsedDateTime = DateTime.parse(dateTime);
    } else if (dateTime.contains('/')) {
      // Format: DD/MM/YYYY HH:MM AM/PM
      final parts = dateTime.split(' ');
      final dateParts = parts[0].split('/');
      final day = int.parse(dateParts[0]);
      final month = int.parse(dateParts[1]);
      final year = int.parse(dateParts[2]);

      var hour = int.parse(parts[1]);
      final minute = int.parse(parts[3]);

      final isPM = parts.last.toUpperCase() == 'PM';

      if (isPM && hour != 12) {
        hour += 12;
      } else if (!isPM && hour == 12) {
        hour = 0;
      }

      parsedDateTime = DateTime(year, month, day, hour, minute);
    } else {
      // Format: YYYY-MM-DD HH:MM:SS.SSSSSS
      final parts = dateTime.split(' ');
      final dateParts = parts[0].split('-');
      final timeParts = parts[1].split(':');

      final year = int.parse(dateParts[0]);
      final month = int.parse(dateParts[1]);
      final day = int.parse(dateParts[2]);

      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);
      final secondsAndMicroseconds = timeParts[2].split('.');
      final second = int.parse(secondsAndMicroseconds[0]);
      final microsecond = secondsAndMicroseconds.length > 1
          ? int.parse(secondsAndMicroseconds[1])
          : 0;

      parsedDateTime =
          DateTime(year, month, day, hour, minute, second, microsecond);
    }

    // Ensure the datetime is in UTC and format it correctly
    return parsedDateTime.toUtc().toIso8601String();
  } catch (e) {
    return ''; // Return an empty string or handle the error as appropriate
  }
}

DateTime parseCustomDateTime(String dateTimeString) {
  // Remove all whitespaces from the input string
  String cleanedString = dateTimeString.replaceAll(RegExp(r'\s+'), '');

  // Use regular expressions to extract parts
  RegExp dateRegex = RegExp(r'(\d{2}/\d{2}/\d{4})');
  RegExp timeRegex = RegExp(r'(\d{2}):?(\d{2})(AM|PM)', caseSensitive: false);

  String? datePart = dateRegex.firstMatch(cleanedString)?.group(1);
  Match? timeMatch = timeRegex.firstMatch(cleanedString);

  if (datePart == null || timeMatch == null) {
    throw FormatException('Invalid date time format: $dateTimeString');
  }

  // Parse date
  DateFormat dateFormat = DateFormat('dd/MM/yyyy');
  DateTime date = dateFormat.parse(datePart);

  // Parse time
  int hours = int.parse(timeMatch.group(1)!);
  int minutes = int.parse(timeMatch.group(2)!);
  bool isPM = timeMatch.group(3)!.toLowerCase() == 'pm';

  // Adjust hours for PM
  if (isPM && hours != 12) {
    hours += 12;
  } else if (!isPM && hours == 12) {
    hours = 0;
  }

  // Combine date and time
  return DateTime(
    date.year,
    date.month,
    date.day,
    hours,
    minutes,
  );
}

// String convertSingleStringToISO8601(String dateTime) {
//   DateTime parsedDateTime = parseCustomDateTime(dateTime);
//   return parsedDateTime.toIso8601String();
// }