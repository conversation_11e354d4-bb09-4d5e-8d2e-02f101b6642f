import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/dashboard/pages/comming_soon/comming_soon.dart';

import '../../../../../utils/utils_exports.dart';

class ChatbotEntryPage extends StatelessWidget {
  const ChatbotEntryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.stackBlue,
      body: Center(
        child: Container(
          margin: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Image.asset(
                "assets/images/sphere-155819_1280.png",
                width: 200.sp,
              )
                  .animate(
                    onComplete: (controller) =>
                        controller.repeat(reverse: true),
                  )
                  .shimmer(
                    duration: const Duration(milliseconds: 2000),
                    delay: const Duration(milliseconds: 1000),
                  )
                  .move(),
              Text(
                "meet_onekitty_ai_assistant".tr,
                textAlign: TextAlign.center,
                style: context.loginTextLarge?.copyWith(
                    fontWeight: FontWeight.bold, color: AppColors.slate),
              ),
              CustomKtButton(
                btnText: 'start'.tr,
                onPress: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const CommingSoon()));
                },
              ).animate().scale(
                    duration: const Duration(milliseconds: 1000),
                    delay: const Duration(milliseconds: 500),
                  )
            ],
          ),
        ),
      ),
    );
  }
}
