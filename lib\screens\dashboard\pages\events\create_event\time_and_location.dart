import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/map_page.dart';
import 'package:onekitty/utils/my_text_field.dart';

class TimeAndLocation extends StatelessWidget {
  final TextEditingController eventStartDate,
      referralCode,
      eventEndDate,
      venue,
      location;
  final GlobalKey<FormState> formKey;
  const TimeAndLocation({
    super.key,
    required this.eventStartDate,
    required this.eventEndDate,
    required this.venue,
    required this.location,
    required this.formKey,
    required this.referralCode,
  });

  @override
  Widget build(BuildContext context) {
    return GetX<TimeAndLocationController>(builder: (controller) {
      return Form(
          key: formKey,
          child: SingleChildScrollView(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                SizedBox(height: 8.h),
                MyTextFieldwValidator(
                  readOnly: true,
                  onTap: () async {
                    DateTime? pickedDateTime = await showDatePicker(
                      context: context,
                      initialDate: DateTime.now(),
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );

                    if (pickedDateTime != null) {
                      TimeOfDay? pickedTime = await showTimePicker(
                        context: context,
                        initialTime: TimeOfDay.now(),
                      );

                      if (pickedTime != null) {
                        DateTime finalDateTime = DateTime(
                          pickedDateTime.year,
                          pickedDateTime.month,
                          pickedDateTime.day,
                          pickedTime.hour,
                          pickedTime.minute,
                        );

                        String formattedDateTime =
                            DateFormat('dd/MM/yyyy HH:mm a')
                                .format(finalDateTime);
                        eventStartDate.text = formattedDateTime;
                      }
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'event_start_date_required'.tr;
                    }
                    return null;
                  },
                  controller: eventStartDate,
                  titleStyle: TextStyle(
                      fontSize: 14.spMin, fontWeight: FontWeight.w500),
                  iconSuffix: IconButton(
                    icon: const Icon(Icons.calendar_month),
                    onPressed: () async {
                      DateTime? pickedDateTime = await showDatePicker(
                        context: context,
                        initialDate: DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );

                      if (pickedDateTime != null) {
                        TimeOfDay? pickedTime = await showTimePicker(
                          context: context,
                          initialTime: TimeOfDay.now(),
                        );

                        if (pickedTime != null) {
                          DateTime finalDateTime = DateTime(
                            pickedDateTime.year,
                            pickedDateTime.month,
                            pickedDateTime.day,
                            pickedTime.hour,
                            pickedTime.minute,
                          );

                          String formattedDateTime =
                              DateFormat('dd/MM/yyyy HH:mm a')
                                  .format(finalDateTime);
                          eventStartDate.text = formattedDateTime;
                        }
                      }
                    },
                  ),
                  hint: 'event_date_time_example'.tr,
                  title: 'event_start_date_time'.tr,
                ),
                SizedBox(height: 8.h),
                MyTextFieldwValidator(
                  readOnly: true,
                  onTap: () async {
                    DateTime? pickedDateTime = await showDatePicker(
                      context: context,
                      initialDate: DateTime.now(),
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );

                    if (pickedDateTime != null) {
                      TimeOfDay? pickedTime = await showTimePicker(
                        context: context,
                        initialTime: TimeOfDay.now(),
                      );

                      if (pickedTime != null) {
                        DateTime finalDateTime = DateTime(
                          pickedDateTime.year,
                          pickedDateTime.month,
                          pickedDateTime.day,
                          pickedTime.hour,
                          pickedTime.minute,
                        );

                        String formattedDateTime =
                            DateFormat('dd/MM/yyyy HH:mm a')
                                .format(finalDateTime);
                        eventEndDate.text = formattedDateTime;
                      }
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'event_end_date_required'.tr;
                    }
                    return null;
                  },
                  controller: eventEndDate,
                  titleStyle: TextStyle(
                      fontSize: 14.spMin, fontWeight: FontWeight.w500),
                  iconSuffix: IconButton(
                    icon: const Icon(Icons.calendar_month),
                    onPressed: () async {
                      DateTime? pickedDateTime = await showDatePicker(
                        context: context,
                        initialDate: DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );

                      if (pickedDateTime != null) {
                        TimeOfDay? pickedTime = await showTimePicker(
                          context: context,
                          initialTime: TimeOfDay.now(),
                        );

                        if (pickedTime != null) {
                          DateTime finalDateTime = DateTime(
                            pickedDateTime.year,
                            pickedDateTime.month,
                            pickedDateTime.day,
                            pickedTime.hour,
                            pickedTime.minute,
                          );

                          String formattedDateTime =
                              DateFormat('dd/MM/yyyy HH:mm a')
                                  .format(finalDateTime);
                          eventEndDate.text = formattedDateTime;
                        }
                      }
                    },
                  ),
                  hint: 'event_date_time_example'.tr,
                  title: 'event_end_date_time'.tr,
                ),
                Text(
                  'event_type'.tr,
                  style: TextStyle(
                      fontSize: 14.spMin, fontWeight: FontWeight.w600),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          TextButton.icon(
                              onPressed: () {
                                controller.eventType.value =
                                    0; // Update the value directly
                              },
                              label: Text('physical_event'.tr),
                              icon: Icon(controller.eventType.value == 0
                                  ? Icons.radio_button_checked
                                  : Icons.radio_button_off)),
                        ],
                      ),
                    ),
                    Expanded(
                      child: TextButton.icon(
                          onPressed: () {
                            controller.eventType.value =
                                1; // Update the value directly
                          },
                          label: Text('online_event'.tr),
                          icon: Icon(controller.eventType.value == 1
                              ? Icons.radio_button_checked
                              : Icons.radio_button_off)),
                    )
                  ],
                ),
                MyTextFieldwValidator(
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'event_venue_required'.tr;
                      }
                      return null;
                    },
                    controller: venue,
                    titleStyle: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.w500),
                    title: 'venue'.tr,
                    hint: 'kicc_example'.tr),
                OutlinedButton(
                  onPressed: () async {
                    final results = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const MapScreen(),
                      ),
                    );
                    if (results != null) {
                      controller.mapCoordinates = {
                        "lat": double.parse(results['latitude'].toString()),
                        "long": double.parse(results['longitude'].toString())
                      }.obs;
                    }
                  },
                  child: Text('choose_on_map'.tr),
                ),
                MyTextFieldwValidator(
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'location_required'.tr;
                      }
                      return null;
                    },
                    controller: location,
                    titleStyle: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.w500),
                    title: 'location_tip'.tr,
                    hint: 'enter_location'.tr),
                MyTextFieldwValidator(
                  keyboardType: TextInputType.number,
                    controller: referralCode,
                    titleStyle: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.w500),
                    title: 'referral_code_optional'.tr,
                    hint: 'enter_referral_code'.tr),
              ])));
    });
  }
}

class TimeAndLocationController extends GetxController implements GetxService {
  RxInt eventType = 0.obs;
  // Initialize with default values to prevent null safety issues
  RxMap<String, double?>  mapCoordinates = {"lat": null, "long": null}.obs;
  
  @override
  void onInit() {
    super.onInit();
    // Ensure mapCoordinates is properly initialized
    if (!mapCoordinates.containsKey('lat') || !mapCoordinates.containsKey('long')) {
      mapCoordinates.value = {"lat": null, "long": null};
    }
  }

  /// Clean up all controller variables
  void cleanupController() {
    eventType.value = 0;
    mapCoordinates.value = {"lat": null, "long": null};
  }

  @override
  void onClose() {
    cleanupController();
    super.onClose();
  }
}
