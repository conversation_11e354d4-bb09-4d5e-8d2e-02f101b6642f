import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/models/chama/chama_settings.dart';
import 'package:onekitty/screens/dashboard/pages/chama/settings/edit_chama_setings.dart';
import 'package:onekitty/utils/app_bar/custom_app_bar.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/custom_button.dart';

class ChamaSettings extends StatefulWidget {
  const ChamaSettings({super.key});

  @override
  State<ChamaSettings> createState() => _ChamaSettingsState();
}

class _ChamaSettingsState extends State<ChamaSettings> {
  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());

  @override
  void initState() {
    super.initState();
    getValues();
  }

  getValues() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      chamaController.getChamaSettings(
          chamaId: chamaDataController.chama.value.chama?.id ?? 0);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //appBar: buildAppBar(context),
      body: SafeArea(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 12),
          child: Column(
            children: [
              const RowAppBar(),
              Text(
                'chama_settings'.tr,
                style: context.titleText,
              ),
              const SizedBox(
                height: 5,
              ),
                Text(
                'settings_description'.tr,
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 5,
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                height: 200.h,
                width: double.infinity,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    image: const DecorationImage(
                        image: AssetImage(AssetUrl.chamaSetting),
                        fit: BoxFit.cover)),
              )
                  .animate()
                  .rotate(
                      duration: const Duration(milliseconds: 5000),
                      curve: Curves.bounceInOut)
                  .shimmer(duration: const Duration(milliseconds: 5000)),
              Obx(
                () => ColumnContainer(
                    onTap: () {
                      showInfoDialog(
                          'get_to_know_beneficiaries'.tr,
                          'beneficiaries_per_cycle'.tr,
                          KtStrings.benefPerCycle,
                          'beneficiary_percentage'.tr,
                          KtStrings.benefPercentage);
                    },
                    firstText: 'beneficiary_per_cycle'.tr,
                    secondText: chamaController.benefPerCycle.value.toString(),
                    thirdText: 'beneficiary_percentage'.tr,
                    fourthText:
                        "${(chamaController.benefPercentage.value * 100)}%"),
              ),
              Obx(() => ColumnContainer(
                  onTap: () {
                    showInfoDialog(
                        'get_to_know_signatories'.tr,
                        'signatories'.tr,
                        KtStrings.sigThreshold,
                        'signatory_threshold'.tr,
                        KtStrings.sigThreshold);
                  },
                  firstText: 'signatories'.tr,
                  secondText: chamaController.signatories.length.toString(),
                  thirdText: 'signatory_threshold'.tr,
                  fourthText:
                      chamaController.signatureThreshold.value.toString())),
              const SizedBox(
                height: 15,
              ),
              if (chamaDataController.chama.value.member?.role == "CHAIRPERSON")
                CustomKtButton(
                  btnText: 'edit'.tr,
                  onPress: () {
                    Get.off(
                        () => EditChamaSettings(chamaSetting: ChamaSetting()));
                  },
                )
            ],
          ),
        ),
      ),
    );
  }

  showInfoDialog(
      String title, String text1, String text2, String text3, String text4) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            backgroundColor: AppColors.slate,
            icon: const Icon(
              Icons.info,
              color: AppColors.blueButtonColor,
              size: 30,
            ),
            //title: Text(title, style: TextStyle(fontWeight: FontWeight.bold),),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  text1,
                  style:
                      context.titleText?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(text2),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  text3,
                  style:
                      context.titleText?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(text4),
              ],
            ),
          );
        });
  }
}

class ColumnContainer extends StatelessWidget {
  final String firstText;
  final String secondText;
  final String thirdText;
  final Function()? onTap;
  final String fourthText;

  const ColumnContainer({
    super.key,
    required this.firstText,
    required this.secondText,
    required this.thirdText,
    required this.onTap,
    required this.fourthText,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.only(top: 18.0),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
              border: Border.all(color: AppColors.blueButtonColor),
              borderRadius: BorderRadius.circular(20)),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    firstText,
                    style: context.titleMedium?.copyWith(
                        color: AppColors.mainPurple,
                        fontWeight: FontWeight.w600),
                  ),
                  Text(
                    secondText,
                    style: context.titleText,
                  )
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    thirdText,
                    style: context.titleMedium?.copyWith(
                        color: AppColors.mainPurple,
                        fontWeight: FontWeight.w600),
                  ),
                  Text(fourthText, style: context.titleText)
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
