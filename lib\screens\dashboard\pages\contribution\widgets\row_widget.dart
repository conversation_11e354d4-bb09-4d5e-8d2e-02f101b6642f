import 'package:flutter/material.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/menu_items.dart';
import 'package:popover/popover.dart';

class SingleLineRow extends StatelessWidget {
  final String text;
  final String? popup;
  final Widget? widget;
  const SingleLineRow({super.key, required this.text, this.widget, this.popup});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(text,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
        ),
        widget ??
            IconButton(
                onPressed: () {
                  showPopover(
                    context: context,
                    bodyBuilder: (context) => MenuItems(
                      text: popup ?? "",
                    ),
                    width: 250,
                    height: 150,
                  );
                },
                icon: const Icon(Icons.info_outline))
      ],
    );
  }
}
