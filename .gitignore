# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/
.augment/
.kilocode/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
**/kernel_blob.bin
**/app.dill
**/flutter_assets/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
android/app/debug/
android/app/profile/
android/app/release/
android/app/build/
android/.gradle/
android/.gradle/**
android/**/*.bin
android/**/executionHistory/
android/**/fileHashes/
android/**/checksums/
android/**/dependencies/
flutter_build_release.bat
flutter_build_debug.bat
flutter build.bat
node_modules/
lib/test_page.dart
.qodo

# Additional Gradle ignores
android/.gradle
android/**/executionHistory.bin
android/**/executionHistory.lock
android/**/fileHashes.bin
android/**/fileHashes.lock
android/**/resourceHashesCache.bin
android/build/
android/**/build/

# Additioget-filter-transactionsnal Flutter ignores
**/kernel_blob.bin
**/app.dill
**/flutter_assets/
android/.kotlin/sessions/kotlin-compiler-3746644442446975756.salive
android/app/.cxx/
shorebird.yaml
.clinerules/byterover-rules.md
.kilocode/rules/byterover-rules.md
.roo/rules/byterover-rules.md
.windsurf/rules/byterover-rules.md
.cursor/rules/byterover-rules.mdc
.kiro/steering/byterover-rules.md
.qoder/rules/byterover-rules.md
.augment/rules/byterover-rules.md