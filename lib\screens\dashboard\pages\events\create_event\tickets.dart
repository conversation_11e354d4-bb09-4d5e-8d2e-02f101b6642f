import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
// import 'package:intl/intl.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/controllers.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/create_event_controller.dart';
import 'package:onekitty/models/events/tickets_model.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/show_snackbar.dart';


class Tickets extends StatefulWidget {
  const Tickets({super.key});

  @override
  State<Tickets> createState() => _TicketsState();
}

class _TicketsState extends State<Tickets> {
  final GlobalKey<FormState> _formKey = GlobalKey();
  final group = false.obs;
  final isLoading = false.obs;

  late final TextEditingController ticketType;
  late final TextEditingController ticketDescription;
  late final TextEditingController price;
  late final TextEditingController purchaseStartDate;
  late final TextEditingController purchaseEndDate;
  late final TextEditingController slotsAvailable;
  late final TextEditingController groupSizeController;

  @override
  void initState() {
    super.initState();
    ticketType = TextEditingController();
    ticketDescription = TextEditingController();
    price = TextEditingController();
    purchaseStartDate = TextEditingController();
    purchaseEndDate = TextEditingController();
    slotsAvailable = TextEditingController();
    groupSizeController = TextEditingController();
  }

  @override
  void dispose() {
    ticketType.dispose();
    ticketDescription.dispose();
    price.dispose();
    purchaseStartDate.dispose();
    purchaseEndDate.dispose();
    slotsAvailable.dispose();
    groupSizeController.dispose();
    super.dispose();
  }

  void _clearForm() {
    ticketType.clear();
    ticketDescription.clear();
    price.clear();
    purchaseStartDate.clear();
    purchaseEndDate.clear();
    slotsAvailable.clear();
    groupSizeController.clear();
    group.value = false;
    Get.find<CreateEventController>().slotType.value = 1;
  }

  Future<DateTime?> _pickDateTime(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date == null) return null;

    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (time == null) return null;

    return DateTime(
      date.year,
      date.month,
      date.day,
      time.hour,
      time.minute,
    );
  }

  bool _validateDates() {
    try {
      final start =
          DateFormat('dd/MM/yyyy hh:mm a').parse(purchaseStartDate.text);
      final end = DateFormat('dd/MM/yyyy hh:mm a').parse(purchaseEndDate.text);

      if (end.isBefore(start)) {
        showSnackbar(
          context: context,
          label: 'end_date_after_start_date'.tr,
          // isError: true
        );
        return false;
      }
      return true;
    } catch (e) {
      showSnackbar(
        context: context,
        label: 'invalid_date_format'.tr,
        // isError: true
      );
      return false;
    }
  }

  Future<void> _handleAddTicket() async {
    if (ticketType.text.isEmpty) {
      showSnackbar(
        context: context,
        label: 'must_pick_ticket_type'.tr,
        // isError: true
      );
      return;
    }

    if (!(_formKey.currentState?.validate() ?? false)) return;
    if (!_validateDates()) return;

    isLoading.value = true;
    try {
      final controller = Get.find<CreateEventController>();
      controller.tickets.add(
        Ticket(
            groupSize:
                group.value ? int.tryParse(groupSizeController.text) : null,
            ticketType: ticketType.text,
            description: ticketDescription.text,
            // quantity: controller.slotType.value == 0 ? "Limited" : "Unlimited",
            startDate:
                DateFormat('dd/MM/yyyy hh:mm a').parse(purchaseStartDate.text),
            endDate:
                DateFormat('dd/MM/yyyy hh:mm a').parse(purchaseEndDate.text),
            quantity: int.tryParse(slotsAvailable.text) ?? 0,
            price: ticketType.text.toLowerCase() == 'free' ? null : int.tryParse(price.text)),
      );
      _clearForm();
    } catch (e) {
      showSnackbar(
        context: context,
        label: 'failed_to_add_ticket'.tr.replaceAll('{error}', e.toString()),
        // isError: true
      );
    } finally {
      isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetX<CreateEventController>(builder: (controller) {
      return ListView.builder(
          shrinkWrap: true,
          itemCount: controller.tickets.length + 1,
          itemBuilder: (context, index) {
            if (index == controller.tickets.length) {
              return Column(
                children: [
                  SizedBox(height: 8.h),
                  if (controller.tickets.isNotEmpty) const Divider(),
                  SizedBox(height: 20.h),
                  Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: MyDropdownMenu(
                                    titleStyle: TextStyle(
                                        fontSize: 14.spMin,
                                        fontWeight: FontWeight.w500),
                                    title: 'ticket_type'.tr,
                                    onSelected: (val) {
                                      if (val == "GROUP") {
                                        group(true);
                                      } else {
                                        group(false);
                                      }
                                      setState((){});
                                    },
                                    lists: Get.find<GlobalControllers>()
                                            .enums
                                            .value
                                            .ticketType
                                            .isEmpty
                                        ? ['GROUP', 'PAID', 'FREE']
                                        : Get.find<GlobalControllers>()
                                            .enums
                                            .value
                                            .ticketType,
                                    controller: ticketType,
                                  ),
                                ),
                                Obx(() => group.value
                                    ? Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Column(
                                          children: [
                                            Text(
                                              'group_size'.tr,
                                              style: TextStyle(
                                                  fontSize: 14.spMin,
                                                  fontWeight: FontWeight.w600),
                                            ),
                                            SizedBox(
                                              width: 120,
                                              child: Expanded(
                                                child: TextField(
                                                  keyboardType:
                                                      TextInputType.number,
                                                  controller:
                                                      groupSizeController,
                                                  decoration:  InputDecoration(
                                                      label: Text('size_label'.tr),
                                                      border:
                                                          const OutlineInputBorder()),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    : const SizedBox())
                              ],
                            ),
                            SizedBox(height: 8.h),
                            MyTextFieldwValidator(
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'ticket_description_required'.tr;
                                  }
                                  return null;
                                },
                                controller: ticketDescription,
                                titleStyle: TextStyle(
                                    fontSize: 14.spMin,
                                    fontWeight: FontWeight.w500),
                                title: 'ticket_description'.tr,
                                hint: 'club_member_example'.tr),
                            SizedBox(height: 8.h),
                            Text(
                              'slots_available'.tr,
                              style: TextStyle(
                                  fontSize: 14.spMin,
                                  fontWeight: FontWeight.w600),
                            ),
                            Obx(() => Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        children: [
                                          TextButton.icon(
                                              onPressed: () {
                                                controller.slotType.value =
                                                    0; // Update the value directly
                                              },
                                              label:
                                                  Text('limited_slots'.tr),
                                              icon: Icon(controller
                                                          .slotType.value ==
                                                      0
                                                  ? Icons.radio_button_checked
                                                  : Icons.radio_button_off)),
                                          if (controller.slotType.value == 0)
                                            MyTextFieldwValidator(
                                                keyboardType:
                                                    TextInputType.number,
                                                validator: (value) {
                                                  if (value == null ||
                                                      value.isEmpty) {
                                                    return 'slots_required'.tr;
                                                  }
                                                  return null;
                                                },
                                                controller: slotsAvailable,
                                                titleStyle: const TextStyle(
                                                    fontSize: 14,
                                                    fontWeight:
                                                        FontWeight.w500),
                                                title: 'slots_available'.tr,
                                                hint: 'eg_100'.tr)
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: TextButton.icon(
                                          onPressed: () {
                                            controller.slotType.value =
                                                1; // Update the value directly
                                          },
                                          label: Text('unlimited_slots'.tr),
                                          icon: Icon(
                                              controller.slotType.value == 1
                                                  ? Icons.radio_button_checked
                                                  : Icons.radio_button_off)),
                                    )
                                  ],
                                )),
                            ticketType.text.toLowerCase() != 'free'
                                ? MyTextFieldwValidator(
                                    keyboardType: TextInputType.number,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'price_required'.tr;
                                      }
                                      return null;
                                    },
                                    controller: price,
                                    titleStyle: const TextStyle(
                                        fontSize: 14, fontWeight: FontWeight.w500),
                                    title: 'price'.tr,
                                    hint: 'price_500_example'.tr)
                                : const SizedBox(),
                            SizedBox(height: 8.h),
                            MyTextFieldwValidator(
                                readOnly: true,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'purchase_start_date_required'.tr;
                                  }
                                  return null;
                                },
                                controller: purchaseStartDate,
                                titleStyle: TextStyle(
                                    fontSize: 14.spMin,
                                    fontWeight: FontWeight.w500),
                                iconSuffix: IconButton(
                                  icon: const Icon(Icons.calendar_month),
                                  onPressed: () async {
                                    DateTime? pickedDateTime =
                                        await _pickDateTime(context);

                                    if (pickedDateTime != null) {
                                      String formattedDateTime =
                                          DateFormat('dd/MM/yyyy hh:mm a')
                                              .format(pickedDateTime);
                                      purchaseStartDate.text =
                                          formattedDateTime;
                                    }
                                  },
                                ),
                                title: 'purchase_start_date_time'.tr,
                                hint: 'date_time_example'.tr),
                            SizedBox(width: 18.w),
                            MyTextFieldwValidator(
                              readOnly: true,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'purchase_end_date_required'.tr;
                                }
                                return null;
                              },
                              controller: purchaseEndDate,
                              titleStyle: TextStyle(
                                  fontSize: 14.spMin,
                                  fontWeight: FontWeight.w500),
                              iconSuffix: IconButton(
                                icon: const Icon(Icons.calendar_month),
                                onPressed: () async {
                                  DateTime? pickedDateTime =
                                      await _pickDateTime(context);

                                  if (pickedDateTime != null) {
                                    String formattedDateTime =
                                        DateFormat('dd/MM/yyyy hh:mm a')
                                            .format(pickedDateTime);
                                    purchaseEndDate.text = formattedDateTime;
                                  }
                                },
                              ),
                              hint: 'date_time_example'.tr,
                              title: 'purchase_end_date_time'.tr,
                            ),
                            SizedBox(height: 18.h),
                            Align(
                              alignment: Alignment.center,
                              child: Obx(() => isLoading.value
                                  ? const CircularProgressIndicator()
                                  : MyButton(
                                      icon: Icons.add,
                                      onClick: _handleAddTicket,
                                      label: 'add_ticket'.tr,
                                    )),
                            )
                          ]),
                    ),
                  ),
                ],
              );
            }

            return Card(
                child: ExpansionTile(
              onExpansionChanged: (expanded) {
                if (expanded) {
                  ticketType.text = controller.tickets[index].ticketType??'';
                  ticketDescription.text =
                      controller.tickets[index].description??'';
                  price.text = controller.tickets[index].price.toString();
                  purchaseStartDate.text = controller.tickets[index].startDate == null ? ''  : DateFormat('dd/MM/yyyy hh:mm a')
                      .format(controller.tickets[index].startDate!);
                  purchaseEndDate.text = controller.tickets[index].endDate == null ? ''  : DateFormat('dd/MM/yyyy hh:mm a')
                      .format(controller.tickets[index].endDate!);
                  slotsAvailable.text =
                      controller.tickets[index].quantity.toString();
                  groupSizeController.text =
                      controller.tickets[index].groupSize?.toString() ?? "0";
                  controller.slotType.value =
                      controller.tickets[index].ticketType == "Limited" ? 0 : 1;
                }
              },
              leading: CircleAvatar(
                backgroundColor: primaryColor,
                radius: 15,
                child: Text('${index + 1}',
                    style: const TextStyle(color: Colors.white)),
              ),
              title: Text(controller.tickets[index].ticketType??''),
              subtitle: Text(controller.tickets[index].description?? ''),
              trailing: IconButton(
                  icon: const Icon(
                    Icons.delete,
                    color: Colors.red,
                  ),
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: Text('confirm_deletion'.tr),
                          content: Text(
                              'confirm_delete_ticket'.tr),
                          actions: [
                            TextButton(
                              child: Text('cancel'.tr),
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                            ),
                            TextButton(
                              child: Text('delete'.tr,
                                  style: const TextStyle(color: Colors.red)),
                              onPressed: () {
                                controller.tickets.removeAt(index);
                                Navigator.of(context).pop();
                              },
                            ),
                          ],
                        );
                      },
                    );
                  }),
              children: [
                Row(
                  children: [
                    Expanded(
                      child: MyDropdownMenu(
                        title: 'ticket_type_label'.tr,
                        onSelected: (val) {
                          if (val == "GROUP") {
                            group(true);
                          } else {
                            group(false);
                          }
                        },
                        lists:
                            Get.put(GlobalControllers()).enums.value.ticketType,
                        controller: ticketType,
                      ),
                    ),
                    Obx(() => group.value
                        ? Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              children: [
                                Text(
                                  'group_size_label'.tr,
                                  style: TextStyle(
                                      fontSize: 14.spMin,
                                      fontWeight: FontWeight.w600),
                                ),
                                SizedBox(
                                  width: 120,
                                  child: Expanded(
                                    child: TextField(
                                      keyboardType: TextInputType.number,
                                      controller: groupSizeController,
                                      decoration:  InputDecoration(
                                          label: Text('size_label'.tr),
                                          border: const OutlineInputBorder()),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : const SizedBox())
                  ],
                ),
                SizedBox(height: 8.h),
                MyTextFieldwValidator(
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'ticket_description_required_msg'.tr;
                      }
                      return null;
                    },
                    controller: ticketDescription,
                    titleStyle: TextStyle(
                        fontSize: 14.spMin, fontWeight: FontWeight.w500),
                    title: 'ticket_description_label'.tr,
                    hint: 'club_member_example'.tr),
                SizedBox(height: 8.h),
                Text(
                  'slots_available_label'.tr,
                  style: TextStyle(
                      fontSize: 14.spMin, fontWeight: FontWeight.w600),
                ),
                Obx(() => Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              TextButton.icon(
                                  onPressed: () {
                                    controller.slotType.value =
                                        0; // Update the value directly
                                  },
                                  label: Text('limited_slots_label'.tr),
                                  icon: Icon(controller.slotType.value == 0
                                      ? Icons.radio_button_checked
                                      : Icons.radio_button_off)),
                              if (controller.slotType.value == 0)
                                MyTextFieldwValidator(
                                    keyboardType: TextInputType.number,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'slots_required_msg'.tr;
                                      }
                                      return null;
                                    },
                                    controller: slotsAvailable,
                                    titleStyle: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500),
                                    title: 'slots_available_title'.tr,
                                    hint: 'eg_100'.tr)
                            ],
                          ),
                        ),
                        Expanded(
                          child: TextButton.icon(
                              onPressed: () {
                                controller.slotType.value =
                                    1; // Update the value directly
                              },
                              label: Text('unlimited_slots'.tr),
                              icon: Icon(controller.slotType.value == 1
                                  ? Icons.radio_button_checked
                                  : Icons.radio_button_off)),
                        )
                      ],
                    )),
                 ticketType.text.toLowerCase() != 'free'
                    ? MyTextFieldwValidator(
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'price_required_msg'.tr;
                          }
                          return null;
                        },
                        controller: price,
                        titleStyle: const TextStyle(
                            fontSize: 14, fontWeight: FontWeight.w500),
                        title: 'price_label'.tr,
                        hint: 'price_500_example'.tr)
                    : const SizedBox(),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Expanded(
                        child: MyTextFieldwValidator(
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'purchase_start_date_required_msg'.tr;
                              }
                              return null;
                            },
                            onTap: () async {
                              DateTime? pickedDateTime =
                                  await _pickDateTime(context);

                              if (pickedDateTime != null) {
                                String formattedDateTime =
                                    DateFormat('dd/MM/yyyy hh:mm a')
                                        .format(pickedDateTime);
                                purchaseStartDate.text = formattedDateTime;
                              }
                            },
                            controller: purchaseStartDate,
                            titleStyle: TextStyle(
                                fontSize: 14.spMin,
                                fontWeight: FontWeight.w500),
                            iconSuffix: IconButton(
                              icon: const Icon(Icons.calendar_month),
                              onPressed: () async {
                                DateTime? pickedDateTime =
                                    await _pickDateTime(context);

                                if (pickedDateTime != null) {
                                  String formattedDateTime =
                                      DateFormat('dd/MM/yyyy hh:mm a')
                                          .format(pickedDateTime);
                                  purchaseStartDate.text = formattedDateTime;
                                }
                              },
                            ),
                            title: 'purchase_start_date_label'.tr,
                            hint: 'date_example'.tr)),
                    SizedBox(width: 18.w),
                    Expanded(
                        child: MyTextFieldwValidator(
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'purchase_end_date_required_msg'.tr;
                        }
                        return null;
                      },
                      controller: purchaseEndDate,
                      onTap: () async {
                        DateTime? pickedDateTime = await _pickDateTime(context);

                        if (pickedDateTime != null) {
                          String formattedDateTime =
                              DateFormat('dd/MM/yyyy hh:mm a')
                                  .format(pickedDateTime);
                          purchaseEndDate.text = formattedDateTime;
                        }
                      },
                      titleStyle: TextStyle(
                          fontSize: 14.spMin, fontWeight: FontWeight.w500),
                      iconSuffix: IconButton(
                        icon: const Icon(Icons.calendar_month),
                        onPressed: () async {
                          DateTime? pickedDateTime =
                              await _pickDateTime(context);

                          if (pickedDateTime != null) {
                            String formattedDateTime =
                                DateFormat('dd/MM/yyyy hh:mm a')
                                    .format(pickedDateTime);
                            purchaseEndDate.text = formattedDateTime;
                          }
                        },
                      ),
                      hint: 'date_example'.tr,
                      title: 'purchase_end_date_label'.tr,
                    )),
                  ],
                ),
                SizedBox(height: 18.h),
                Align(
                  alignment: Alignment.center,
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      if (ticketType.text == "") {
                        showSnackbar(
                            context: context,
                            label: 'must_pick_ticket_type_proceed'.tr);
                        return;
                      }
                      if (_formKey.currentState?.validate() ?? false) {
                        if (!_validateDates()) return;
                        controller.tickets[index] = Ticket(
                            ticketType: ticketType.text,
                            description: ticketDescription.text,
                            // : controller.slotType.toInt() == 0
                            //     ? "Limited"
                            //     : "Unlimited",
                            startDate: DateFormat('dd/MM/yyyy hh:mm a')
                                .parse(purchaseStartDate.text),
                            endDate: DateFormat('dd/MM/yyyy hh:mm a')
                                .parse(purchaseEndDate.text),
                            quantity: int.parse(slotsAvailable.text == ""
                                ? "0"
                                : slotsAvailable.text),
                            price: ticketType.text.toLowerCase() == 'free' ? null : int.tryParse(price.text));
                      }
                    },
                    label: Text('edit_ticket_label'.tr),
                  ),
                )
              ],
            ));
          });
    });
  }
}
