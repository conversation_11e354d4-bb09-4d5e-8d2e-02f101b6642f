// To parse this JSON data, do
//
//     final paymentChannels = paymentChannelsFromJson(jsonString);

import 'dart:convert';

List<PaymentChannels> paymentChannelsFromJson(String str) =>
    List<PaymentChannels>.from(
        json.decode(str).map((x) => PaymentChannels.fromJson(x)));

String paymentChannelsToJson(List<PaymentChannels> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class PaymentChannels {
  final int id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final int channelCode;
  final String name;
  final String description;
  final Status status;
  final String imageUrl;
  final String displayMessage;
  final Category category;
  final bool typeIn;
  final bool typeOut;

  PaymentChannels({
    this.id = 0,
     this.createdAt,
     this.updatedAt,
    this.deletedAt,
    this.channelCode = 0,
    this.name = '',
    this.description = '',
    this.status = Status.ACTIVE,
    this.imageUrl = '',
    this.displayMessage = '',
    this.category = Category.EMPTY,
    this.typeIn = true,
    this.typeOut = true,
  });

  factory PaymentChannels.fromJson(Map<String, dynamic> json) =>
      PaymentChannels(
        id: json["ID"] ?? 0,
        createdAt: DateTime.tryParse(json["CreatedAt"] ?? '') ?? DateTime.now(),
        updatedAt: DateTime.tryParse(json["UpdatedAt"] ?? '') ?? DateTime.now(),
        deletedAt: json["DeletedAt"],
        channelCode: json["channel_code"] ?? 0,
        name: json["name"] ?? '',
        description: json["description"] ?? '',
        status: statusValues.map[json["status"]] ?? Status.ACTIVE,
        imageUrl: json["image_url"] ?? '',
        displayMessage: json["display_message"] ?? '',
        category: categoryValues.map[json["category"]] ?? Category.EMPTY,
        typeIn: json["type_in"] ?? true,
        typeOut: json["type_out"] ?? true,
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toUtc().toIso8601String(),
        "UpdatedAt": updatedAt?.toUtc().toIso8601String(),
        "DeletedAt": deletedAt,
        "channel_code": channelCode,
        "name": name,
        "description": description,
        "status": statusValues.reverse[status],
        "image_url": imageUrl,
        "display_message": displayMessage,
        "category": categoryValues.reverse[category],
        
      };
}

enum Category { BANK, EMPTY }

final categoryValues = EnumValues({"BANK": Category.BANK, "": Category.EMPTY});

enum Status { ACTIVE }

final statusValues = EnumValues({"ACTIVE": Status.ACTIVE});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
