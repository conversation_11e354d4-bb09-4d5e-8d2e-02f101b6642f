import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';

import '../../../../../utils/size_config.dart';
import '../../../../../utils/utils_exports.dart';

class Signatories extends StatefulWidget {
  const Signatories({super.key});

  @override
  State<Signatories> createState() => _SignatoriesState();
}

class _SignatoriesState extends State<Signatories> {
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //appBar: buildAppBar(context),
      body: SafeArea(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
          child: Column(
            children: [
              const RowAppBar(),
              Text('signatories_title'.tr,
                  style: Theme.of(context)
                      .textTheme
                      .titleLarge
                      ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22)),
              SizedBox(
                height: 5.h,
              ),
                Text(
                'signatories_description'.tr,
                textAlign: TextAlign.center,
              ),
              SizedBox(
                height: 15.h,
              ),
              Expanded(
                child: GetX(
                    init: ChamaController(),
                    initState: (state) {
                      Future.delayed(Duration.zero, () async {
                        try {
                          await state.controller?.getSignatories(
                              chamaId:
                                  chamaDataController.chama.value.chama?.id ??
                                      0);
                        } catch (e) {
                          throw e;
                        }
                      });
                    },
                    builder: (ChamaController chamaController) {
                      if (chamaController.isGetSignatoryLoading.isTrue) {
                        return SizedBox(
                          height: SizeConfig.screenHeight * .33,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SpinKitDualRing(
                                  color: ColorUtil.blueColor,
                                  lineWidth: 4.sp,
                                  size: 40.0.sp,
                                ),
                                Text(
                                  'loading'.tr,
                                  style: const TextStyle(
                                    color: Colors.white,
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      } else if (chamaController.signatories.isEmpty) {
                        return Center(
                          child: Column(
                            children: [
                              Image.asset(
                                AssetUrl.notFound,
                                height: 130.h,
                              ),
                              Text('no_signatories_added'.tr),
                              const SizedBox(
                                height: 10,
                              ),
                            ],
                          ),
                        );
                      } else if (chamaController.signatories.isNotEmpty) {
                        return Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 2.h, vertical: 17.h),
                          decoration: AppDecoration.outlineGray.copyWith(
                              borderRadius: BorderRadiusStyle.roundedBorder8),
                          child: ListView.separated(
                              separatorBuilder: (context, index) {
                                return const Divider();
                              },
                              itemCount: chamaController.signatories.length,
                              itemBuilder: (context, index) {
                                final signatory =
                                    chamaController.signatories[index];
                                return Row(
                                  children: [
                                    Row(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Text("${index + 1}"),
                                        ),
                                        CustomImageView(
                                          imagePath: AssetUrl.imgPerson,
                                          height: 25.h,
                                          width: 25.w,
                                          margin: EdgeInsets.only(left: 3.h),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                              "${signatory.member?.firstName}"),
                                          Text(signatory.phoneNumber ?? ""),
                                          Text(signatory.notificationType ?? "")
                                        ]),
                                    const Spacer(),
                                    Text(signatory.member?.role ?? ""),
                                    const Spacer(),
                                    if (chamaDataController
                                            .chama.value.member?.role ==
                                        "CHAIRPERSON")
                                      IconButton(
                                          onPressed: () async {
                                            showDeleteDialog(index);
                                          },
                                          icon: const Icon(Icons.close_rounded))
                                  ],
                                );
                              }),
                        );
                      }
                      return Text('no_signatories_added'.tr);
                    }),
              ),
              SizedBox(
                height: 10.h,
              ),
              SizedBox(
                height: 40,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    OutlinedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Text('back'.tr),
                        )),
                    if (chamaDataController.chama.value.member?.role ==
                        "CHAIRPERSON")
                      CustomKtButton(
                        onPress: () {
                          Get.offNamed(NavRoutes.addSignatory);
                        },
                        btnText: 'add_signatory'.tr,
                        width: 150.w,
                      )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  deleteSignatory(index) async {
    final signatory = chamaController.signatories[index];
    bool res = await chamaController.deleteSignatory(
        signatoryId: signatory.id ?? 0, chamaId: signatory.chamaId ?? 0);
    if (res) {
      Snack.show(res, chamaController.apiMessage.string);
      setState(() {
        chamaController.signatories.removeAt(index);
      });
    }
  }

  void showDeleteDialog(index) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            actions: [
              OutlinedButton(
                  onPressed: () {
                    Get.back();
                  },
                  child: Text('no'.tr)),
              Obx(() => CustomKtButton(
                  width: 55.w,
                  height: 35.h,
                  isLoading: chamaController.isDeleteSignatoryLoading.isTrue,
                  onPress: () {
                    deleteSignatory(index);
                    Navigator.pop(context);
                  },
                  btnText: 'yes'.tr))
            ],
            content:
                Text('are_you_sure_delete_signatory'.tr),
          );
        });
  }
}
