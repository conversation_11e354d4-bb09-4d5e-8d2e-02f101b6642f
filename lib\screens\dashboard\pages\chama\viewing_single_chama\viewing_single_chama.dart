// ignore_for_file: prefer_const_literals_to_create_immutables, prefer_const_constructors
import 'dart:io';
import 'dart:ui';
import 'package:onekitty/main.dart' show isLight;
import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/chama_profile.dart';
import 'package:onekitty/screens/dashboard/pages/transactions/controllers/transaction_controller.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/my_quill_editor.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/chama/tabs/chama_services.dart';
import 'package:onekitty/screens/dashboard/pages/transactions/views/screens/transaction_page.dart';
import 'package:onekitty/screens/dashboard/pages/transactions/models/transaction_type.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/whatsapp/whatsapp_widget.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_image_view.dart';
import 'package:onekitty/utils/custom_text_style.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/utils/themes_colors.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';

class ViewingSingleChama extends StatefulWidget {
  const ViewingSingleChama({super.key});

  @override
  State<ViewingSingleChama> createState() => _ViewingSingleChamaState();
}

class _ViewingSingleChamaState extends State<ViewingSingleChama> {
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());

  final PageController _pageController =
      PageController(initialPage: 1, viewportFraction: 0.8);

  final ChamaController chamaController = Get.put(ChamaController());
  // q.QuillController descr = q.QuillController.basic();
  List<String> circleImages = [
    AssetUrl.group6,
    AssetUrl.winter,
    AssetUrl.imgEllipse1,
  ];

  GlobalKey globalKey = GlobalKey();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: true);

  void _onRefresh() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await chamaController.getAllChamaDetails(
          chamaId: chamaDataController.singleChamaDts.value.id ?? 0);
      chamaDataController.singleChamaDts.value =
          chamaController.chamaDetails.value;
      await chamaController.getBenficiaries(
          chamaId: chamaDataController.singleChamaDts.value.id ?? 0);
    });

    _refreshController.refreshCompleted();
  }

  @override
  void initState() {
    super.initState();
  }

  // void readDescr() {
  //   try {
  //     var jsonF = jsonDecode(
  //         chamaDataController.singleChamaDts.value.description ?? "");
  //     descr = q.QuillController(
  //       document: q.Document.fromJson(jsonF),
  //       selection: const TextSelection.collapsed(offset: 0),
  //     );
  //   } catch (e) {}
  // }

  @override
  void dispose() {
    if (_pageController.hasClients) {
      _pageController.dispose();
    }
    if (_refreshController.isRefresh) {
      _refreshController.dispose();
    }
    super.dispose();
  }

  /// Get kittyId safely from multiple sources
  int? _getKittyId() {
    // Try to get from chamaDataController first
    final kittyIdFromChama = chamaDataController.chama.value.chama?.kittyId;
    if (kittyIdFromChama != null) {

      return kittyIdFromChama;
    }
    
    // Fallback to chamaController
    final kittyIdFromController = chamaController.chamaDetails.value.kittyId;
    if (kittyIdFromController != null) {
      return kittyIdFromController;
    }
    
    // Fallback to singleChamaDts
    final kittyIdFromSingle = chamaDataController.singleChamaDts.value.kittyId;
    if (kittyIdFromSingle != null) {
      return kittyIdFromSingle;
    }
    
    // Log warning if no kittyId found
    if (kDebugMode) {
      print('Warning: No kittyId found in any chama data source');
    }
    
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        // backgroundColor: isLight.value ? appTheme.gray50 : appTheme.gray900,
        //appBar: buildAppBar(context),
        body: SmartRefresher(
          controller: _refreshController,
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: _onRefresh,
          child: CustomScrollView(
            slivers: [
              Obx(
                () => SliverAppBar(
                  leadingWidth: 100.w,
                  leading: InkWell(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black45,
                          borderRadius: BorderRadius.circular(25),
                        ),
                        margin: const EdgeInsets.all(6),
                        padding: const EdgeInsets.all(4),
                        alignment: Alignment.center,
                        child:   Row(children: [
                          Icon(Icons.navigate_before, color: Colors.white),
                          Text('back'.tr, style: TextStyle(color: Colors.white))
                        ])),
                  ),
                  expandedHeight:
                      chamaController.profileUrl.value == '' ? 0 : 250.h,
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                  pinned: true,
                  flexibleSpace: Obx(() =>
                      chamaController.profileUrl.value == ''
                          ? const SizedBox()
                          : ChamaFlexibleSpacebar()),
                  actions: chamaController.profileUrl.value == ''
                      ? [
                          SizedBox(width: 8.w),
                          InkWell(
                            onTap: () async {
                              if (chamaController.isUploadingImage.value) {
                                return;
                              }
                              chamaController.pickImage(
                                  context: context, kittyId: 0, name: '');
                            },
                            child: Obx(
                              () => chamaController.isUploadingImage.value
                                  ? const CircleAvatar(
                                      backgroundColor: Colors.black38,
                                      child: CircularProgressIndicator(
                                          backgroundColor: Colors.white))
                                  : const CircleAvatar(
                                      backgroundColor: Colors.black38,
                                      child: Icon(Icons.add_a_photo_outlined,
                                          color: Colors.white)),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          InkWell(
                            onTap: () async {
                              String shareMsg =
                                  "${'chama_title'.tr}: ${chamaDataController.singleChamaDts.value.title ?? ""}\n${'click_link'.tr}: https://onekitty.co.ke/chama/${chamaDataController.singleChamaDts.value.kittyId ?? 0}\n ${'to_pay'.tr}";
                              await Share.share(shareMsg,
                                  subject: 'chama_details'.tr);
                            },
                            child: const CircleAvatar(
                                backgroundColor: Colors.black38,
                                child: Icon(Icons.share, color: Colors.white)),
                          ),
                          SizedBox(width: 8.w),
                          InkWell(
                            onTap: () {
                              showModalBottomSheet(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10.0),
                                ),
                                context: context,
                                isScrollControlled: true,
                                constraints: BoxConstraints(
                                  maxHeight: SizeConfig.screenHeight * .8,
                                  maxWidth: SizeConfig.screenWidth,
                                ),
                                builder: (_) => SizedBox(
                                  height: SizeConfig.screenHeight * .7,
                                  width: SizeConfig.screenWidth,
                                  child: Column(
                                    children: [
                                      const SizedBox(
                                        height: 10,
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.all(15.0),
                                        child: RepaintBoundary(
                                          key: globalKey,
                                          child: Container(
                                            margin: const EdgeInsets.all(20.0),
                                            color: Colors.white,
                                            child: Column(
                                              children: [
                                                  Text(
                                                  'scan_to_pay'.tr,
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 20,
                                                  ),
                                                ),
                                                QrImageView(
                                                  padding: const EdgeInsets.all(
                                                      10.0),
                                                  data:
                                                      "onekitty.co.ke/chama/${chamaDataController.singleChamaDts.value.kittyId}",
                                                  version: QrVersions.auto,
                                                  gapless: false,
                                                  errorCorrectionLevel:
                                                      QrErrorCorrectLevel.H,
                                                  embeddedImage: AssetImage(
                                                      AssetUrl.logo4),
                                                  embeddedImageStyle:
                                                      const QrEmbeddedImageStyle(
                                                    size: Size(80, 80),
                                                  ),
                                                  size: 250.0,
                                                ),
                                                const SizedBox(
                                                  height: 5.0,
                                                ),
                                                Text(
                                                  "  onekitty.co.ke/chama/${chamaDataController.singleChamaDts.value.kittyId} ",
                                                  style: const TextStyle(
                                                    color: Colors.blue,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                                const SizedBox(height: 10),
                                                Text(
                                                  chamaDataController
                                                          .singleChamaDts
                                                          .value
                                                          .title ??
                                                      "" "\n",
                                                  style: const TextStyle(
                                                      fontSize: 17),
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        margin:
                                            const EdgeInsets.only(right: 10.0),
                                        child: ElevatedButton(
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Theme.of(context)
                                                  .colorScheme
                                                  .primary,
                                              fixedSize: const Size(250, 20),
                                            ),
                                            child: Text(
                                              'share'.tr,
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 15,
                                              ),
                                            ),
                                            onPressed: () async {
                                              _captureAndSharePng();
                                            }),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                            child: const CircleAvatar(
                                backgroundColor: Colors.black38,
                                child:
                                    Icon(Icons.qr_code_2, color: Colors.white)),
                          ),
                        ]
                      : [],
                ),
              ),
              // SliverToBoxAdapter(child: RowAppBar()),
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 31.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            chamaDataController.singleChamaDts.value.title ??
                                "",
                            style: CustomTextStyles.labelMediumff545963,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(
                            width: 15.w,
                          ),
                        ],
                      ),
                      SizedBox(height: 8.h),
                      Obx(() => Visibility(
                            visible: (chamaDataController
                                        .singleChamaDts.value.totaBal ??
                                    0.0) >
                                0.0,
                            child: Column(
                              children: [
                                Text(
                                    "${'balance'.tr} ${FormattedCurrency.getFormattedCurrency(chamaDataController.singleChamaDts.value.balance ?? 0.0)}",
                                    style:
                                        CustomTextStyles.titleMediumSemiBold),
                              ],
                            ),
                          )),
                      Padding(
                        padding: EdgeInsets.only(
                          left: 4.w,
                          top: 4.h,
                        ),
                        child: Obx(
                          () => Visibility(
                            visible: chamaController.penaltyBal > 0,
                            child: RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: "${'penalties'.tr} ${'amount_paid'.tr}: ",
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontStyle: FontStyle.italic,
                                      color: appTheme.gray90001,
                                    ),
                                  ),
                                  TextSpan(
                                    text: FormattedCurrency
                                        .getFormattedCurrency(
                                      chamaController.penaltyBal,
                                    ),
                                    style: TextStyle(
                                      fontStyle: FontStyle.italic,
                                      color: appTheme.gray90001,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 6.h),
                      QuillEditorWidget(
                          text: chamaDataController
                                  .singleChamaDts.value.description ??
                              ''),

                      // ReadMoreText(descr.document.toPlainText(),
                      //     trimMode: TrimMode.Line,
                      //     trimLines: 2,
                      //     trimCollapsedText: 'show more',
                      //     trimExpandedText: 'show less',
                      //     colorClickableText: ColorUtil.blueColor,
                      //     style: CustomTextStyles.bodySmallBluegray700),
                      // buildShareIcons(context),
                      SizedBox(height: 6.h),
                      _buildFrame(context),
                      SizedBox(height: 31.h),
                      Align(
                        alignment: Alignment.topLeft,
                        child: Text(
                          'next_beneficiary'.tr,
                          style:
                              context.dividerTextLarge?.copyWith(fontSize: 15),
                        ),
                      ),
                      SizedBox(
                        height: 5.h,
                      ),
                      _buildFrame1(context),
                      SizedBox(height: 6.h),
                      GetBuilder(builder: (ChamaController chamaController) {
                        if (chamaController.notifications.isEmpty) {
                          return SizedBox.shrink();
                        } else {
                          return _buildRow(context);
                        }
                      }),
                      SizedBox(height: 6.h),
                      _buildFrame2(),
                      SizedBox(height: 10.h),
                    ],
                  ),
                ),
              ),
              SliverToBoxAdapter(child: _buildTabs(context)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFrame(BuildContext context) {
    final isLightMode = isLight.value;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: isLightMode ? Colors.white : appTheme.gray900,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Member Avatars
          _buildMemberAvatars(),
          SizedBox(height: 16.h),

          // Contribution Info
          _buildInfoRow(
            icon: AssetUrl.money,
            text:
                "${FormattedCurrency.getFormattedCurrency(chamaDataController.chama.value.chama?.amount ?? 0)}/${chamaDataController.chama.value.chama?.frequency}",
          ),

          // Penalty Info
          _buildInfoRow(
            icon: AssetUrl.penalty2,
            text: "${'penalties'.tr} ${'accrued'.tr}: ${chamaController.penaltyCount}",
          ),
          SizedBox(height: 12.h),

          // Cycle Timeline
          _buildCycleTimeline(isLightMode),
        ],
      ),
    );
  }

  Widget _buildMemberAvatars() {
    final avatarCount = circleImages.length;
    final stackWidth = (avatarCount * 40.w) - (20.w * (avatarCount - 1));

    return Row(
      children: [
        SizedBox(
          width: stackWidth,
          height: 40.h,
          child: Stack(
            clipBehavior: Clip.none,
            children: List.generate(
              avatarCount,
              (index) => Positioned(
                left: index * 24.w,
                child: CircleAvatar(
                  radius: 20.r,
                  backgroundColor: Colors.white,
                  child: CircleAvatar(
                    radius: 18.r,
                    backgroundImage: AssetImage(circleImages[index]),
                  ),
                ),
              ),
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Text(
          "${chamaDataController.chama.value.membersCount} ${'members_count'.tr}",
          style: TextStyle(
            fontSize: 14.sp,
            color: isLight.value ? appTheme.gray700 : appTheme.whiteA700,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow({
    required String icon,
    required String text,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        children: [
          CustomImageView(
            imagePath: icon,
            height: 20.h,
            width: 20.w,
            color: isLight.value ? appTheme.gray900 : appTheme.whiteA700,
          ),
          SizedBox(width: 12.w),
          Text(
            text,
            style: TextStyle(
              fontSize: 14.sp,
              color: isLight.value ? appTheme.gray900 : appTheme.whiteA700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCycleTimeline(bool isLightMode) {
    final nextOccurrence =
        chamaDataController.singleChamaDts.value.nextOccurrence?.toLocal() ??
            DateTime.now();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CustomImageView(
              imagePath: AssetUrl.imgClock,
              height: 20.h,
              width: 20.w,
              color: isLightMode ? appTheme.gray900 : appTheme.whiteA700,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                DateTimeFormat.relative(nextOccurrence,
                    levelOfPrecision: 1,
                    prependIfBefore: 'next_cycle_in'.tr,
                    ifNow: 'now'.tr,
                    appendIfAfter: 'ago'.tr),
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: isLightMode ? appTheme.gray900 : appTheme.whiteA700,
                ),
              ),
            ),
          ],
        ),
        Padding(
          padding: EdgeInsets.only(left: 32.w, top: 8.h),
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '${'deadline'.tr}: ',
                  style: TextStyle(
                    fontSize: 13.sp,
                    fontWeight: FontWeight.w500,
                    color: isLightMode ? appTheme.gray600 : appTheme.whiteA700,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                TextSpan(
                  text: DateFormat('MMM dd, yyyy').format(nextOccurrence),
                  style: TextStyle(
                    fontSize: 13.sp,
                    fontWeight: FontWeight.w600,
                    color: isLightMode ? appTheme.gray900 : appTheme.whiteA700,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRow(BuildContext context) {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      Expanded(
        child: Text(
          'connected_whatsapp_groups'.tr,
          style: TextStyle(fontSize: 13.h, color: appTheme.gray900),
        ),
      ),
      CustomImageView(
        imagePath: AssetUrl.addGif,
        height: 15.h,
        width: 15.w,
        // margin: EdgeInsets.only(bottom: 2.h),
      ),
      if (chamaDataController.chama.value.member?.role == "CHAIRPERSON")
        Padding(
            padding: EdgeInsets.only(left: 2.w),
            child: InkWell(
                onTap: () {
                  Get.toNamed(NavRoutes.addGrouplink);
                },
                child: Text('add_group'.tr,
                    style: CustomTextStyles.titleSmallIndigo500)))
    ]);
  }

  Widget _buildTabs(BuildContext context) {
    final _transcontroller = Get.isRegistered<TransactionController>() ? Get.find<TransactionController>() : Get.put(TransactionController()) ;
     _transcontroller.currentKittyId.value = _getKittyId()??0;
    return Column(
      children: [
        DefaultTabController(
            length: 2,
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 12),
                  padding: EdgeInsets.all(5.h),
                  decoration: AppDecoration.fillSlate.copyWith(
                    borderRadius: BorderRadiusStyle.roundedBorder6,
                  ),
                  child: TabBar(
                    physics: const ClampingScrollPhysics(),
                    //padding: const EdgeInsets.only(left: 5, right: 5),
                    unselectedLabelColor:
                        isLight.value ? Colors.black : Colors.white70,
                    labelColor: Theme.of(context).primaryColor,
                    indicatorSize: TabBarIndicatorSize.tab,
                    dividerColor: Colors.transparent,

                    indicator: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: isLight.value ? Colors.white : Colors.black45),
                    tabs: [
                      Tab(
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 1.w),
                          child: Text('services'.tr),
                        ),
                      ),
                      Tab(
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 1.w),
                          child: Text('transactions'.tr),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 10.h,
                ),
                SizedBox(
                  height: MediaQuery.sizeOf(context).height *0.9,
                  child: Column(
                    children: [
                      Expanded(
                        child: TabBarView(
                          children: [
                            const ChamaServicesWidget(),
                            TransactionPage(
                              kittyId: _getKittyId(), 
                              config: TransactionPageConfig(
                                transactionType: TransactionType.chama,
                                entityId: chamaDataController.chama.value.chama?.id, 
                                kittyId: _getKittyId(), 
                                title: 'Chama Transactions',
                                isFullPage: false,
                                showExportOptions: true,
                                showEditOptions: false,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ))
      ],
    );
  }

  Widget _buildFrame1(BuildContext context) {
    return GetX(
      init: ChamaController(),
      initState: (state) {
        Future.delayed(Duration.zero, () async {
          try {
            // await state.controller?.getBenficiaries(
            //     chamaId: chamaDataController.singleChamaDts.value.id ?? 0);
            // chamaController.reset();
          } catch (e) {}
        });
      },
      builder: (ChamaController controller) {
        if (controller.isgetRes.isTrue) {
          return SizedBox(
            height: SizeConfig.screenHeight * .1,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitDualRing(
                    color: ColorUtil.blueColor,
                    lineWidth: 4.sp,
                    size: 40.0.sp,
                  ),
                  Text(
                    'loading'.tr,
                    style: const TextStyle(
                      color: Colors.white,
                    ),
                  )
                ],
              ),
            ),
          );
        } else if (controller.beneficiaries.isEmpty) {
          return Text(
            'no_beneficiaries_found'.tr,
            style: context.dividerTextLarge,
            textAlign: TextAlign.center,
          );
        } else if (controller.beneficiaries.isNotEmpty) {
          return SizedBox(
            height: 100.h,
            child: PageView.builder(
              controller: _pageController,
              scrollDirection: Axis.horizontal,
              itemCount: controller.beneficiaries.length,
              itemBuilder: (context, index) {
                final beneficiary = controller.beneficiaries[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 3.0),
                  child: InkWell(
                    onTap: () {
                      final dataController = Get.put(ChamaDataController());
                      dataController.singleBenf.value = NextBeneficiary(
                        beneficiaryPercentage:
                            beneficiary.beneficiaryPercentage,
                        amountToReceive: beneficiary.amountToReceive,
                        member: beneficiary.member,
                        beneficiaries: beneficiary.beneficiaries,
                      );
                      if (dataController.chama.value.member!.id ==
                              beneficiary.member!.id ||
                          dataController.chama.value.member!.role ==
                              "CHAIRPERSON" ||
                          dataController.chama.value.member!.role ==
                              "TREASURER" ||
                          dataController.chama.value.member!.role ==
                              "SECRETARY") {
                        Get.toNamed(NavRoutes.beneficiary);
                      }
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: isLight.value
                                ? appTheme.whiteA700
                                : Colors.transparent,
                            border: Border.all(color: appTheme.gray200),
                          ),
                          child: Row(
                            children: [
                              CircleAvatar(
                                radius: 20,
                                backgroundImage:
                                    AssetImage(AssetUrl.imgEllipse1),
                              ),
                              SizedBox(
                                width: 7.w,
                              ),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                          '${beneficiary.member!.firstName ?? ""} ${beneficiary.member!.secondName}',
                                          style: context.dividerTextLarge,
                                        ),
                                      ],
                                    ),
                                    Text(
                                      beneficiary.member!.role ?? "",
                                      style: context.dividerTextLarge
                                          ?.copyWith(color: appTheme.gray600),
                                    ),
                                    Text(
                                      FormattedCurrency.getFormattedCurrency(
                                          beneficiary.amountToReceive),
                                      style: context.dividerTextLarge
                                          ?.copyWith(color: appTheme.green800),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          );
        }

        return Text(
          'error_loading_data'.tr,
          style: context.dividerTextLarge,
          textAlign: TextAlign.center,
        );
      },
    );
  }

  Widget _buildFrame2() {
    return GetX(
        init: ChamaController(),
        initState: (state) {
          Future.delayed(Duration.zero, () async {
            try {
              await state.controller?.getAllChamaDetails(
                  chamaId: chamaDataController.chama.value.chama?.id ?? 0);
            } catch (e) {}
          });
        },
        builder: (ChamaController chamaController) {
          if (chamaController.isGetAllChamaDetailsLoading.isTrue) {
            return SizedBox(
              height: SizeConfig.screenHeight * .1,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SpinKitDualRing(
                      color: ColorUtil.blueColor,
                      lineWidth: 4.sp,
                      size: 40.0.sp,
                    ),
                    Text(
                      'loading_ellipsis'.tr,
                      style: const TextStyle(
                        color: Colors.white,
                      ),
                    )
                  ],
                ),
              ),
            );
          } else if (chamaController.notifications.isEmpty) {
            if (chamaDataController.chama.value.member?.role == "CHAIRPERSON") {
              return InkWell(
                  onTap: () {
                    Get.toNamed(NavRoutes.addGrouplink);
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          'no_whatsapp_groups_connected'.tr,
                          style: context.dividerTextLarge,
                        ),
                      ),
                      Text(
                        'add_group'.tr,
                        style: context.dividerTextLarge
                            ?.copyWith(color: AppColors.blueButtonColor),
                      ),
                    ],
                  ));
            } else {
              return Expanded(child: Text('no_whatsapp_groups_connected'.tr));
            }
          } else if (chamaController.notifications.isNotEmpty) {
            return ListView.separated(
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  final whatsapp = chamaController.notifications[index];
                  return ChamaWhatsappWidget(whatsapp: whatsapp);
                },
                separatorBuilder: (context, index) {
                  return SizedBox(
                    height: 5,
                  );
                },
                itemCount: chamaController.notifications.length);
          }
          return Text('an_error_occurred'.tr);
        });
  }

  Widget buildShareIcons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        InkWell(
          onTap: () async {
            String shareMsg =
                "${'chama_title'.tr}: ${chamaDataController.singleChamaDts.value.title ?? ""}\n${'click_link'.tr}: https://onekitty.co.ke/chama/${chamaDataController.singleChamaDts.value.kittyId ?? 0}\n ${'to_pay'.tr}";
            await Share.share(shareMsg, subject: 'chama_details'.tr);
          },
          child: Padding(
            padding: EdgeInsets.only(right: 40.w),
            child: Container(
              padding: EdgeInsets.only(left: 15.w),
              child: CustomImageView(
                height: 15.h,
                width: 15.w,
                imagePath: AssetUrl.share,
              ),
            ),
          ),
        ),
        InkWell(
          onTap: () {
            showModalBottomSheet(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              context: context,
              isScrollControlled: true,
              constraints: BoxConstraints(
                maxHeight: SizeConfig.screenHeight * .8,
                maxWidth: SizeConfig.screenWidth,
              ),
              builder: (_) => SizedBox(
                height: SizeConfig.screenHeight * .7,
                width: SizeConfig.screenWidth,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(15.0),
                      child: RepaintBoundary(
                        key: globalKey,
                        child: Container(
                          margin: const EdgeInsets.all(20.0),
                          color: Colors.white,
                          child: Column(
                            children: [
                              Text(
                                'scan_to_pay'.tr,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 20,
                                ),
                              ),
                              QrImageView(
                                padding: const EdgeInsets.all(10.0),
                                data:
                                    "onekitty.co.ke/chama/${chamaDataController.singleChamaDts.value.kittyId}",
                                version: QrVersions.auto,
                                gapless: false,
                                errorCorrectionLevel: QrErrorCorrectLevel.H,
                                embeddedImage: AssetImage(AssetUrl.logo4),
                                embeddedImageStyle: const QrEmbeddedImageStyle(
                                  size: Size(80, 80),
                                ),
                                size: 250.0,
                              ),
                              const SizedBox(
                                height: 5.0,
                              ),
                              Text(
                                "  onekitty.co.ke/chama/${chamaDataController.singleChamaDts.value.kittyId} ",
                                style: const TextStyle(
                                  color: Colors.blue,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 10),
                              Text(
                                chamaDataController
                                        .singleChamaDts.value.title ??
                                    "" "\n",
                                style: const TextStyle(fontSize: 17),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(right: 10.0),
                      child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            fixedSize: const Size(250, 20),
                          ),
                          child: Text(
                            'share'.tr,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 15,
                            ),
                          ),
                          onPressed: () async {
                            _captureAndSharePng();
                          }),
                    ),
                  ],
                ),
              ),
            );
          },
          child: Padding(
            padding: EdgeInsets.only(right: 30.w),
            child: QrImageView(
              data:
                  "https://onekitty.co.ke/chama/${chamaDataController.singleChamaDts.value.kittyId}",
              version: QrVersions.auto,
              gapless: false,
              size: 45,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _captureAndSharePng() async {
    try {
      RenderRepaintBoundary boundary =
          globalKey.currentContext?.findRenderObject() as RenderRepaintBoundary;

      var image = await boundary.toImage(pixelRatio: 5.0);

      ByteData byteData =
          await image.toByteData(format: ImageByteFormat.png) as ByteData;
      Uint8List pngBytes = byteData.buffer.asUint8List();

      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/image.png').create();
      await file.writeAsBytes(pngBytes);

      await Share.shareXFiles(
        [XFile(file.path)],
      );
    } catch (e) {
      if (kDebugMode) {
        print(e.toString());
      }
    }
  }
}
