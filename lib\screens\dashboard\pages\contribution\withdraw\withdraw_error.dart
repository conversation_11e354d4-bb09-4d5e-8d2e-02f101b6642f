import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty/utils/app_bar/custom_app_bar.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_image_view.dart';
import 'package:onekitty/utils/custom_text_style.dart';
import 'package:onekitty/utils/themes_colors.dart';

// ignore: must_be_immutable
class WithdrawError extends StatelessWidget {
  final ContributeController contributeController = Get.find();
  KittyController controller = Get.put(KittyController());
  final DataController dataController = Get.put(DataController());
  WithdrawError({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //appBar: _buildAppBar(context),
      body: Padding(
        padding: EdgeInsets.only(top: 10.h),
        child: SizedBox(
          width: double.maxFinite,
          child: Column(children: [
            const RowAppBar(),
            Text(dataController.kitty.value.kitty?.title ?? "",
                style: theme.textTheme.titleLarge),
            SizedBox(height: 8.h),
            // isRichText
            //     ?
            Align(
              alignment: Alignment.centerLeft,
              child: Container(
                width: 343.w,
                margin: EdgeInsets.only(right: 24.w),
                child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                            text:
                                "${"every_shilling_counts_message".tr} ",
                            style: CustomTextStyles.bodySmallff545963),
                        TextSpan(
                            text: dataController.kitty.value.kitty?.title ?? "",
                            style: CustomTextStyles.labelLargeff545963)
                      ],
                    ),
                    textAlign: TextAlign.center),
              ),
            ),
            // : SizedBox(),
            SizedBox(height: 30.h),
            SizedBox(
              height: 360.h,
              width: 339.w,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  CustomImageView(
                      imagePath: AssetUrl.errorState,
                      height: 300.h,
                      width: 275.w,
                      alignment: Alignment.topCenter),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(controller.withdrawData["status"] ?? "",
                            style: theme.textTheme.titleLarge),
                        SizedBox(height: 2.h),
                        SizedBox(
                          width: 339.w,
                          child: Text(
                              controller.withdrawData["amount"] ??
                                  controller
                                      .withdrawData["response_description"] ??
                                  "",
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                              style: CustomTextStyles.bodyMediumff545963),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 10.h),
            SizedBox(height: 5.h)
          ]),
        ),
      ),
    );
  }

  onTapArrowLeft(BuildContext context) {
    Navigator.pop(context);
  }
}
