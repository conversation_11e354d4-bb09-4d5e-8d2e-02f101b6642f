import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:mime/mime.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/create_event_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/events/categories_model.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/show_snackbar.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:onekitty/main.dart' show isLight;

class EventDetails extends StatelessWidget {
  final TextEditingController eventTitle, emailAddress, phoneNumber;
  final q.QuillController eventDescription;
  // final TextEditingController eventDescription;
  final GlobalKey<FormState> formKey;

  EventDetails(
      {super.key,
      required this.eventTitle,
      required this.eventDescription,
      required this.emailAddress,
      required this.phoneNumber,
      required this.formKey});
  final _controller = Get.find<CreateEventController>();

  Future<void> _uploadSingleFile(file, String tempId) async {
    try {
      final url = await _controller.uploadFile(
          path: file.path!,
          fileName: "${DateTime.now().millisecondsSinceEpoch}_${file.name}");
      
      if (url != null && url.isNotEmpty) {
        final index = _controller.bannerList.indexWhere((item) => item['id'] == tempId);
        if (index != -1) {
          _controller.bannerList[index] = {
            "id": tempId,
            "name": file.path!,
            "uploading": false,
            "url": url
          };
          
          // Check if this media already exists in eventMedia to prevent duplicates
          final existingMediaIndex = _controller.eventMedia.indexWhere((media) => media['id'] == tempId);
          if (existingMediaIndex == -1) {
            // Only add if it doesn't already exist
            _controller.eventMedia.add({'id': tempId, 'url': url, 'type': "image"});
          } else {
            // Update existing media with the URL
            _controller.eventMedia[existingMediaIndex] = {'id': tempId, 'url': url, 'type': "image"};
          }
        }
      } else {
        // Remove from both lists if upload failed
        _controller.bannerList.removeWhere((item) => item['id'] == tempId);
        _controller.eventMedia.removeWhere((media) => media['id'] == tempId);
      }
    } catch (e) {
      // Remove from both lists if upload failed
      _controller.bannerList.removeWhere((item) => item['id'] == tempId);
      _controller.eventMedia.removeWhere((media) => media['id'] == tempId);
    } finally {
      _controller.uploadingCount.value--;
    }
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CreateEventController>();
    return SingleChildScrollView(
      child: Form(
        key: formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MyTextFieldwValidator(
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'event_title_required'.tr;
                  }
                  if (_controller.eventMedia.isEmpty) {
                    return 'event_banner_required'.tr;
                  }
                  return null;
                },
                controller: eventTitle,
                title: 'event_title'.tr,
                hint: 'kenya_awards_night_example'.tr),
            SizedBox(height: 16.h),
            Text(
              'event_description'.tr,
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
            Padding(
              padding: const EdgeInsets.all(4.0),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(color: Colors.grey.shade400),
                ),
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    q.QuillSimpleToolbar(
                      controller: eventDescription,
                      config: const q.QuillSimpleToolbarConfig(
                        multiRowsDisplay: false,
                      ),
                    ),
                    const SizedBox(height: 15),
                    q.QuillEditor.basic(
                      controller: eventDescription,
                      config:   q.QuillEditorConfig(
                        placeholder:
                            'describe_your_event'.tr,

                        // readOnly: false,
                        autoFocus: false,
                        enableInteractiveSelection:
                            true, // Enable interactive selection to allow text editing
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16.h),
            MyTextFieldwValidator(
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'email_address_required'.tr;
                  }
                  if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                    return 'enter_valid_email_address'.tr;
                  }

                  return null;
                },
                keyboardType: TextInputType.emailAddress,
                controller: emailAddress,
                title: 'email_address'.tr,
                hint: 'info_awards_example'.tr),
            SizedBox(height: 16.h),
            CustomInternationalPhoneInput(
              onInputChanged: (
                PhoneNumber number,
              ) {
                phoneNumber.text =
                    number.phoneNumber.toString().replaceAll("+", '');
                print(phoneNumber.text);
              },
               initialPhoneNumber: phoneNumber.text,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'phone_number_required'.tr;
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            Text(
              'category'.tr,
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 8.h),
            GetX<CreateEventController>(
                builder: (controller) {
                  // Ensure categories are loaded
                  if (controller.categories.isEmpty && !controller.isLoadingCategories.value) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      controller.getCategories();
                    });
                  }
                  if (controller.isLoadingCategories.isTrue) {
                    return Container(
                      height: 55.h,
                      padding: const EdgeInsets.all(8),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color:
                            isLight.value ? Colors.white70 : Colors.transparent,
                        border: Border.all(color: Colors.grey, width: 0.5),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child:   Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('select_category'.tr),
                          const CupertinoActivityIndicator()
                        ],
                      ),
                    );
                  } else if (controller.categories.isEmpty) {
                    return Container(
                      height: 55.h,
                      padding: const EdgeInsets.all(8),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color:
                            isLight.value ? Colors.white70 : Colors.transparent,
                        border: Border.all(color: Colors.grey, width: 0.5),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Center(child: Text('no_categories_found'.tr)),
                    );
                  } else {
                    return DropdownButtonFormField<CategoriesModel>(
                      value: controller.selCategory?.value,
                      decoration: InputDecoration(
                        hintText: 'select_category'.tr,
                        filled: true,
                        fillColor:
                            isLight.value ? Colors.white70 : Colors.transparent,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                          borderSide: const BorderSide(
                            width: 0.5,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      items: controller.categories.map((category) {
                        return DropdownMenuItem<CategoriesModel>(
                          value: category,
                          child: Text(category.title ?? ''),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          controller.category.value = value.id!;
                          controller.selCategory?.value = value;
                        }
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'category_required'.tr;
                        }
                        return null;
                      },
                    );
                  }
                }),
            SizedBox(height: 16.h),
            Text(
            'event_images'.tr,
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 8.h),
            Obx(() => GestureDetector(
            onTap: _controller.isMediaLimitReached ? null : () async {
            if (_controller.uploadingCount.value > 0) {
            showSnackbar(
            context: context,
            label: 'wait_for_uploads_complete'.tr,
            color: Colors.orange);
            return;
            }
            
            final result = await FilePicker.platform.pickFiles(
            allowMultiple: true,
            type: FileType.custom,
            allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            );
            
            if (result == null || result.files.isEmpty) {
            ToastUtils.showToast('no_images_selected'.tr);
            return;
            }
            
            final selectedFiles = result.files.where((file) {
            final mimeType = lookupMimeType(file.path ?? '') ?? '';
            return mimeType.startsWith('image/') && file.size <= 10 * 1024 * 1024;
            }).toList();
            
            if (selectedFiles.isEmpty) {
            showSnackbar(
            context: context,
            label: 'no_valid_images_found'.tr,
            color: Colors.red);
            return;
            }
            
            final filesToUpload = selectedFiles.take(_controller.remainingMediaSlots).toList();
            
            _controller.uploadingCount.value = filesToUpload.length;
            
            for (final file in filesToUpload) {
            final tempId = '${DateTime.now().millisecondsSinceEpoch}_${filesToUpload.indexOf(file)}';
            final bannerItem = {
            "id": tempId,
            "name": file.path!,
            "uploading": true,
            "url": null
            };
            
            _controller.bannerList.add(bannerItem);
            
            // Upload asynchronously
            _uploadSingleFile(file, tempId);
            }
            },
            child: Container(
            height: 55.h,
            padding: const EdgeInsets.all(8),
            width: double.infinity,
            decoration: BoxDecoration(
            color: _controller.isMediaLimitReached
            ? Colors.grey.withOpacity(0.3)
            : (isLight.value ? Colors.white70 : Colors.transparent),
            border: Border.all(
            color: _controller.isMediaLimitReached ? Colors.grey.withOpacity(0.5) : Colors.grey
            ),
            borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
            Text(
            'upload_event_images'.tr,
            style: TextStyle(
            fontSize: 14.spMin,
            color: _controller.isMediaLimitReached ? Colors.grey : null,
            ),
            ),
            SizedBox(width: 8.w),
            Obx(() {
            return _controller.uploadingCount.value > 0
            ? Row(
            mainAxisSize: MainAxisSize.min,
            children: [
            const CupertinoActivityIndicator(),
            SizedBox(width: 4.w),
            Text('${_controller.uploadingCount.value}', 
            style: TextStyle(fontSize: 12.sp)),
            ],
            )
            : const Icon(Icons.add_photo_alternate);
            }),
            ],
            ),
            ),
            )),
            SizedBox(
              height: 100,
              child: Obx(() {
                return ListView.builder(
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    itemCount: controller.bannerList.length,
                    itemBuilder: (context, index) {
                      final bannerItem = controller.bannerList[index];
                      final isUploading = bannerItem['uploading'] == true;
                      
                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            Image.file(
                              File("${bannerItem['name']}"),
                              height: 80,
                              width: 80,
                              fit: BoxFit.cover,
                            ),
                            if (isUploading)
                              Container(
                                color: Colors.black54,
                                child: const Center(
                                  child: CircularProgressIndicator(color: Colors.white),
                                ),
                              )
                            else
                              Positioned(
                                top: 0,
                                right: 0,
                                child: IconButton(
                                  icon: const Icon(Icons.close, color: Colors.red),
                                  onPressed: () {
                                    final itemId = bannerItem['id'];
                                    // Remove from both lists using ID matching
                                    controller.bannerList.removeWhere((item) =>
                                        item['id'] == itemId);
                                    controller.eventMedia.removeWhere((media) =>
                                        media['id'] == itemId);
                                  },
                                ),
                              )
                          ],
                        ),
                      );
                    });
              }),
            ),
            SizedBox(height: 200.h)
          ],
        ),
      ),
    );
  }
}
